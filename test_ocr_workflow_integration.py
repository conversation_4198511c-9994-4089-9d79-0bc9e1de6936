"""
Comprehensive Test for OCR Workflow Integration

Tests the complete workflow:
1. Email processing with attachments 
2. Zendesk ticket creation
3. OCR processing with Zurich API
4. Database updates with OCR results
5. Zendesk comments for processing status
6. Comprehensive logging
"""

import asyncio
import os
import tempfile
import uuid
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Set up environment
os.environ.setdefault("ENV", "development")

from src.config.settings import Settings
from src.database.supabase_client import Supabase<PERSON>lient
from src.zendesk_integration.zendesk_client import ZendeskClient
from src.zendesk_integration.claims_processor import ClaimsProcessor
from src.ocr_consensus.ocr_service import ZurichOCRService
from src.utils.dozzle_logger import dozzle_log


class OCRWorkflowTester:
    """Comprehensive test suite for OCR workflow integration"""
    
    def __init__(self):
        self.settings = Settings()
        self.supabase = SupabaseClient(self.settings)
        self.zendesk = ZendeskClient(self.settings)
        self.claims_processor = ClaimsProcessor(self.settings)
        self.test_claim_id = None
        self.test_ticket_id = None
        
    async def test_complete_workflow(self):
        """Test the complete workflow from email to OCR processing"""
        try:
            dozzle_log("info", "🧪 [TEST] Starting comprehensive OCR workflow test")
            
            # Step 1: Test email processing simulation
            workflow_id = f"test_ocr_{uuid.uuid4().hex[:8]}"
            email_data = {
                "sender_email": "<EMAIL>",
                "sender_name": "Test Customer OCR",
                "subject": "OCR Test - Auto Accident Claim with Documents",
                "body": """Dear Zurich Insurance,

I am writing to report an auto accident that occurred yesterday. I have attached several documents for your review:
- Police report
- Photos of vehicle damage  
- Medical records
- Insurance certificate

Please process my claim as soon as possible.

Thank you,
Test Customer""",
                "timestamp": datetime.now().isoformat(),
                "message_id": f"test_ocr_{uuid.uuid4().hex}@test.com"
            }
            
            # Simulate AI classification
            classification_result = {
                "final_analysis": {
                    "is_claim": True,
                    "claim_type": "auto",
                    "urgency_level": "high", 
                    "confidence_level": "high",
                    "extracted_details": {
                        "incident_date": "2024-12-29",
                        "incident_location": "Toronto, ON",
                        "estimated_value": 15000.0,
                        "claimant_name": "Test Customer OCR"
                    }
                }
            }
            
            # Create test attachment files
            attachments = await self._create_test_attachments()
            
            dozzle_log("info", "📧 [TEST] Test email data prepared",
                      workflow_id=workflow_id,
                      attachment_count=len(attachments))
            
            # Step 2: Test complete claims processing with OCR
            processing_result = await self.claims_processor.process_claim_email(
                workflow_id=workflow_id,
                email_data=email_data,
                classification_result=classification_result,
                attachments=attachments
            )
            
            self.test_claim_id = processing_result.get('claim_id')
            self.test_ticket_id = processing_result.get('zendesk_ticket_id')
            
            dozzle_log("info", "✅ [TEST] Claims processing completed",
                      claim_id=self.test_claim_id,
                      ticket_id=self.test_ticket_id,
                      success=processing_result.get('success'),
                      ocr_status=processing_result.get('ocr_status'),
                      ocr_processed=processing_result.get('ocr_processed', 0))
            
            # Step 3: Validate OCR results
            await self._validate_ocr_results()
            
            # Step 4: Test direct OCR service functionality
            await self._test_direct_ocr_service()
            
            # Step 5: Validate database state
            await self._validate_database_state()
            
            # Step 6: Validate Zendesk integration
            await self._validate_zendesk_integration()
            
            dozzle_log("info", "🎉 [TEST] All OCR workflow tests completed successfully!")
            
            return {
                "success": True,
                "claim_id": self.test_claim_id,
                "ticket_id": self.test_ticket_id,
                "processing_result": processing_result
            }
            
        except Exception as e:
            dozzle_log("error", "❌ [TEST] OCR workflow test failed",
                      error=str(e),
                      error_type=type(e).__name__)
            raise
    
    async def _create_test_attachments(self) -> List[Dict[str, Any]]:
        """Create test attachment files for OCR processing"""
        attachments = []
        
        # Create test documents with different content types
        test_files = [
            {
                "filename": "police_report.txt",
                "content": b"""POLICE ACCIDENT REPORT
Date: December 28, 2024
Location: Highway 401 & Don Mills Road, Toronto, ON
Officer: Constable J. Smith, Badge #1234

INCIDENT DETAILS:
- Vehicle 1: 2019 Honda Civic, License: ABC123
- Vehicle 2: 2021 Toyota Camry, License: XYZ789
- Weather: Clear, dry conditions
- Time: 3:30 PM

NARRATIVE:
Vehicle 1 was proceeding eastbound on Highway 401 when Vehicle 2 failed to yield while merging from the on-ramp. Vehicle 2 struck Vehicle 1 on the rear passenger side, causing significant damage.

INJURIES:
Driver of Vehicle 1 complained of neck pain and was transported to Toronto General Hospital.

FAULT DETERMINATION:
Vehicle 2 driver cited for failure to yield right of way.
""",
                "content_type": "text/plain"
            },
            {
                "filename": "medical_report.txt", 
                "content": b"""MEDICAL REPORT
Patient: Test Customer OCR
Date of Service: December 28, 2024
Physician: Dr. Sarah Johnson, MD

CHIEF COMPLAINT:
Neck pain following motor vehicle accident

EXAMINATION:
- Range of motion limited in cervical spine
- Tender to palpation C3-C5 vertebrae
- No neurological deficits noted

ASSESSMENT:
Cervical strain/whiplash injury consistent with MVA

TREATMENT PLAN:
- Physiotherapy 3x weekly for 6 weeks
- NSAIDs for pain management
- Follow-up in 2 weeks

ESTIMATED TREATMENT COST: $2,500
""",
                "content_type": "text/plain"
            },
            {
                "filename": "insurance_certificate.txt",
                "content": b"""CERTIFICATE OF INSURANCE
Policy Number: ZUR-AUTO-12345678
Policyholder: Test Customer OCR
Vehicle: 2019 Honda Civic
VIN: 1HGBH41JXMN109186

COVERAGE DETAILS:
- Liability: $2,000,000
- Collision: $500 deductible
- Comprehensive: $300 deductible
- Medical Payments: $50,000

Policy Period: January 1, 2024 - January 1, 2025
Premium: $1,800 annually

Status: ACTIVE - Premiums paid current
""",
                "content_type": "text/plain"
            },
            {
                "filename": "damage_estimate.txt",
                "content": b"""VEHICLE DAMAGE ESTIMATE
Estimator: Mike's Auto Body Shop
Date: December 29, 2024
Vehicle: 2019 Honda Civic

DAMAGE ASSESSMENT:
Rear Quarter Panel:
- Replace rear passenger door: $1,200
- Repair rear quarter panel: $800
- Paint and refinish: $600

Mechanical:
- Rear suspension inspection: $150
- Wheel alignment: $120

TOTAL ESTIMATE: $2,870
Labor Hours: 18
Parts Cost: $1,800
Paint/Materials: $600
Shop Supplies: $120

COMPLETION TIME: 3-4 business days
""",
                "content_type": "text/plain"
            }
        ]
        
        for file_info in test_files:
            attachments.append({
                "filename": file_info["filename"],
                "content": file_info["content"],
                "content_type": file_info["content_type"],
                "size": len(file_info["content"])
            })
        
        dozzle_log("info", "📎 [TEST] Test attachments created",
                  file_count=len(attachments),
                  total_size=sum(att["size"] for att in attachments))
        
        return attachments
    
    async def _test_direct_ocr_service(self):
        """Test OCR service directly"""
        try:
            dozzle_log("info", "🔍 [TEST] Testing OCR service directly")
            
            # Create OCR service instance
            ocr_service = ZurichOCRService(
                settings=self.settings,
                supabase_client=self.supabase,
                zendesk_client=self.zendesk
            )
            
            # Test OCR processing for the test claim
            if self.test_claim_id and self.test_ticket_id:
                ocr_results = await ocr_service.process_claim_attachments(
                    claim_id=self.test_claim_id,
                    zendesk_ticket_id=self.test_ticket_id,
                    workflow_id=f"direct_test_{uuid.uuid4().hex[:8]}"
                )
                
                dozzle_log("info", "✅ [TEST] Direct OCR service test completed",
                          status=ocr_results.get('status'),
                          processed_count=ocr_results.get('processed_count', 0),
                          total_count=ocr_results.get('total_count', 0))
            else:
                dozzle_log("warning", "⚠️ [TEST] No test claim/ticket available for direct OCR test")
                
        except Exception as e:
            dozzle_log("error", "❌ [TEST] Direct OCR service test failed",
                      error=str(e))
            # Don't raise - this is a supplementary test
    
    async def _validate_ocr_results(self):
        """Validate OCR processing results"""
        try:
            if not self.test_claim_id:
                dozzle_log("warning", "⚠️ [TEST] No test claim ID for OCR validation")
                return
            
            # Get attachments with OCR results
            attachments = await self.supabase.get_claim_attachments(self.test_claim_id)
            
            ocr_processed = 0
            total_text_length = 0
            
            for attachment in attachments:
                ocr_text = attachment.get('ocr_text')
                ocr_confidence = attachment.get('ocr_confidence')
                processed_at = attachment.get('processed_at')
                
                if ocr_text:
                    ocr_processed += 1
                    total_text_length += len(ocr_text)
                    
                    dozzle_log("info", "📄 [TEST] OCR result validated",
                              filename=attachment.get('original_filename'),
                              confidence=ocr_confidence,
                              text_length=len(ocr_text),
                              processed_at=processed_at)
            
            dozzle_log("info", "✅ [TEST] OCR results validation completed",
                      claim_id=self.test_claim_id,
                      total_attachments=len(attachments),
                      ocr_processed=ocr_processed,
                      total_text_extracted=total_text_length)
            
        except Exception as e:
            dozzle_log("error", "❌ [TEST] OCR results validation failed",
                      error=str(e))
            raise
    
    async def _validate_database_state(self):
        """Validate database state after OCR processing"""
        try:
            if not self.test_claim_id:
                return
            
            # Get claim details
            claim = await self.supabase.get_claim_by_id(self.test_claim_id)
            
            if claim:
                dozzle_log("info", "💾 [TEST] Claim state validated",
                          claim_id=self.test_claim_id,
                          status=claim.get('status'),
                          zendesk_ticket_id=claim.get('zendesk_ticket_id'))
            
            # Get claim history
            history_result = self.supabase.client.table('claim_history').select('*').eq('claim_id', self.test_claim_id).execute()
            
            if history_result.data:
                dozzle_log("info", "📚 [TEST] Claim history validated",
                          claim_id=self.test_claim_id,
                          history_entries=len(history_result.data))
                
                for entry in history_result.data:
                    dozzle_log("info", "📝 [TEST] History entry",
                              event_type=entry.get('event_type'),
                              description=entry.get('event_description'),
                              created_at=entry.get('created_at'))
            
        except Exception as e:
            dozzle_log("error", "❌ [TEST] Database state validation failed",
                      error=str(e))
            # Don't raise - this is validation
    
    async def _validate_zendesk_integration(self):
        """Validate Zendesk ticket and comments"""
        try:
            if not self.test_ticket_id:
                dozzle_log("warning", "⚠️ [TEST] No test ticket ID for Zendesk validation")
                return
            
            # Get ticket details from Zendesk
            ticket_details = await self.zendesk.get_ticket_details(self.test_ticket_id)
            
            if ticket_details:
                dozzle_log("info", "🎫 [TEST] Zendesk ticket validated",
                          ticket_id=self.test_ticket_id,
                          status=ticket_details.get('status'),
                          priority=ticket_details.get('priority'),
                          comments_count=len(ticket_details.get('comments', [])))
                
                # Check for OCR-related comments
                comments = ticket_details.get('comments', [])
                ocr_comments = [c for c in comments if 'DOCUMENT PROCESSING' in c.get('body', '')]
                
                dozzle_log("info", "💬 [TEST] OCR comments found",
                          ticket_id=self.test_ticket_id,
                          ocr_comments_count=len(ocr_comments))
            
        except Exception as e:
            dozzle_log("error", "❌ [TEST] Zendesk integration validation failed",
                      error=str(e))
            # Don't raise - this is validation


async def main():
    """Run comprehensive OCR workflow tests"""
    tester = OCRWorkflowTester()
    
    try:
        dozzle_log("info", "🚀 [TEST] Starting OCR workflow integration test suite")
        
        result = await tester.test_complete_workflow()
        
        if result.get('success'):
            dozzle_log("info", "🎉 [TEST] OCR workflow integration test completed successfully!",
                      claim_id=result.get('claim_id'),
                      ticket_id=result.get('ticket_id'))
            
            print("\n" + "="*80)
            print("✅ OCR WORKFLOW INTEGRATION TEST SUCCESSFUL!")
            print("="*80)
            print(f"Claim ID: {result.get('claim_id')}")
            print(f"Zendesk Ticket ID: {result.get('ticket_id')}")
            print(f"OCR Status: {result.get('processing_result', {}).get('ocr_status')}")
            print(f"OCR Processed: {result.get('processing_result', {}).get('ocr_processed', 0)} files")
            print("="*80)
        else:
            print("\n❌ OCR WORKFLOW INTEGRATION TEST FAILED!")
            
    except Exception as e:
        dozzle_log("error", "💥 [TEST] OCR workflow integration test suite failed",
                  error=str(e),
                  error_type=type(e).__name__)
        
        print(f"\n❌ TEST FAILED: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main()) 