#!/usr/bin/env python3
"""
Test script to verify clean file storage without email headers
"""

import asyncio
import sys
import os
from datetime import datetime
import tempfile

# Add src to path
sys.path.append('src')

from database.supabase_client import SupabaseClient
from config.settings import Settings

async def test_clean_file_storage():
    """Test that files are stored cleanly without email headers"""
    
    print("🧪 [TEST] Testing clean file storage...")
    
    try:
        # Initialize clients
        settings = Settings()
        supabase = SupabaseClient(settings)
        
        # Test data: clean file content (no email headers)
        test_claim_id = "test-claim-12345"
        test_workflow_id = "test-workflow-67890" 
        
        # Test 1: Upload a clean PDF file
        print("\n📄 [TEST_1] Testing PDF file upload...")
        
        # Simulate clean PDF content (PDF magic bytes)
        clean_pdf_content = b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f\n0000000009 00000 n\n0000000074 00000 n\n0000000120 00000 n\ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n197\n%%EOF'
        
        # Upload with simple filename
        original_filename = "Certificate of Insurance.pdf"
        
        attachment = await supabase.upload_attachment(
            claim_id=test_claim_id,
            workflow_id=test_workflow_id,
            file_content=clean_pdf_content,
            filename=original_filename,
            content_type="application/pdf"
        )
        
        print(f"✅ [TEST_1] PDF uploaded successfully:")
        print(f"   - Original filename: {attachment['original_filename']}")
        print(f"   - Storage path: {attachment['storage_path']}")
        print(f"   - File size: {attachment['file_size']} bytes")
        
        # Verify simple path format
        expected_path = f"claims/{test_claim_id}/{original_filename}"
        if attachment['storage_path'] == expected_path:
            print(f"✅ [PATH_TEST] Storage path is correct: {expected_path}")
        else:
            print(f"❌ [PATH_TEST] Expected: {expected_path}")
            print(f"❌ [PATH_TEST] Got: {attachment['storage_path']}")
        
        # Test 2: Download and verify content is clean
        print(f"\n📥 [TEST_2] Testing file download...")
        
        downloaded_content = await supabase.download_file(attachment['storage_path'])
        
        if downloaded_content:
            print(f"✅ [TEST_2] Download successful: {len(downloaded_content)} bytes")
            
            # Verify content is clean (no email headers)
            content_str = str(downloaded_content[:200])
            email_header_patterns = [
                'Content-Type:', 'Content-Disposition:', 'MIME-Version:',
                'Message-ID:', 'From:', 'To:', 'Subject:', 'Date:'
            ]
            
            has_headers = any(pattern in content_str for pattern in email_header_patterns)
            
            if not has_headers:
                print(f"✅ [CLEAN_CONTENT] File content is clean (no email headers)")
            else:
                print(f"❌ [CONTAMINATED] File content contains email headers!")
                print(f"   Content preview: {content_str}")
            
            # Verify content matches what we uploaded
            if downloaded_content == clean_pdf_content:
                print(f"✅ [CONTENT_INTEGRITY] File content matches exactly")
            else:
                print(f"❌ [CONTENT_MISMATCH] Downloaded content differs from uploaded")
                print(f"   Uploaded: {len(clean_pdf_content)} bytes")
                print(f"   Downloaded: {len(downloaded_content)} bytes")
        else:
            print(f"❌ [TEST_2] Download failed")
        
        # Test 3: Simulate OCR processing
        print(f"\n🔍 [TEST_3] Testing OCR result storage...")
        
        # Simulate OCR processing results
        ocr_text = "This is to certify that the policy holder has valid insurance coverage."
        ocr_confidence = 0.95
        
        # Update attachment with OCR results
        updated_attachment = await supabase.update_attachment_ocr(
            attachment_id=attachment['id'],
            ocr_text=ocr_text,
            ocr_confidence=ocr_confidence,
            document_type="insurance_certificate",
            processing_metadata={
                "model": "test_ocr",
                "processing_time": 1.5
            },
            processed_at=datetime.utcnow()
        )
        
        print(f"✅ [TEST_3] OCR results stored successfully:")
        print(f"   - OCR text length: {len(updated_attachment['ocr_text'])} characters")
        print(f"   - OCR confidence: {updated_attachment['ocr_confidence']:.1%}")
        print(f"   - Document type: {updated_attachment['document_type']}")
        print(f"   - Original filename preserved: {updated_attachment['original_filename']}")
        
        # Test 4: Verify database structure
        print(f"\n📊 [TEST_4] Testing database structure...")
        
        # Retrieve attachment by ID
        result = supabase.client.table('attachments').select('*').eq('id', attachment['id']).execute()
        
        if result.data:
            db_attachment = result.data[0]
            print(f"✅ [TEST_4] Database record found:")
            print(f"   - Original file: {db_attachment['original_filename']}")
            print(f"   - Storage path: {db_attachment['storage_path']}")
            print(f"   - OCR text: {'✅ Present' if db_attachment['ocr_text'] else '❌ Missing'}")
            print(f"   - OCR confidence: {db_attachment['ocr_confidence']}")
            print(f"   - File size: {db_attachment['file_size']} bytes")
        else:
            print(f"❌ [TEST_4] Database record not found")
        
        print(f"\n🎉 [SUMMARY] Clean file storage test completed!")
        print(f"✅ Original filename preserved: {original_filename}")
        print(f"✅ Simple storage path: claims/{test_claim_id}/{original_filename}")
        print(f"✅ Clean file content (no email headers)")
        print(f"✅ OCR results stored in same database row")
        
        return True
        
    except Exception as e:
        print(f"❌ [ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_clean_file_storage())
    sys.exit(0 if success else 1) 