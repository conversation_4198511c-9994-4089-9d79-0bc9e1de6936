#!/usr/bin/env python3
"""
Test the new temp file extraction approach for email attachments
This should bypass email header contamination completely
"""

import sys
import os
import tempfile
import logging
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from email_processing.email_monitor import EmailMonitor

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_email_with_attachment():
    """Create a test email with a PDF attachment that might be contaminated"""
    
    # Create a multipart message
    msg = MIMEMultipart()
    msg['Subject'] = 'Test Email with Attachment'
    msg['From'] = '<EMAIL>'
    msg['To'] = '<EMAIL>'
    
    # Create a fake PDF content (PDF magic number + some content)
    pdf_content = b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n'
    
    # Add some potential email header contamination
    contaminated_content = b'Return-Path: <<EMAIL>>\nReceived: from mail.example.com\n' + pdf_content
    
    # Create attachment part
    attachment = MIMEBase('application', 'pdf')
    attachment.set_payload(contaminated_content)
    encoders.encode_base64(attachment)
    attachment.add_header('Content-Disposition', 'attachment; filename="test_document.pdf"')
    
    msg.attach(attachment)
    
    return msg.as_bytes()

def test_temp_file_extraction():
    """Test the new temp file extraction method"""

    print("🧪 Testing temp file extraction approach...")

    # Create test email
    email_data = create_test_email_with_attachment()

    # Create a minimal settings object for testing
    class MockSettings:
        def __init__(self):
            self.claims_email = "<EMAIL>"
            self.claims_email_password = "test_password"
            self.imap_server = "imap.example.com"
            self.imap_port = 993
            self.zendesk_token = "test_token"
            self.zendesk_subdomain = "test"
            self.zendesk_email = "<EMAIL>"
            self.supabase_url = "https://test.supabase.co"
            self.supabase_key = "test_key"

    # Initialize EmailMonitor with mock settings
    settings = MockSettings()
    email_monitor = EmailMonitor(settings=settings)
    
    # Parse the email
    import email
    from email.policy import default
    
    msg = email.message_from_bytes(email_data, policy=default)
    
    # Find the attachment part
    attachment_part = None
    for part in msg.walk():
        if part.get_content_disposition() == 'attachment':
            attachment_part = part
            break
    
    if not attachment_part:
        print("❌ No attachment part found in test email")
        return False
    
    print("✅ Found attachment part")
    
    # Test the new temp file extraction method
    temp_file_path = email_monitor._extract_payload_to_temp_file(attachment_part, "test_document.pdf")
    
    if not temp_file_path:
        print("❌ Temp file extraction failed")
        return False
    
    print(f"✅ Temp file created: {temp_file_path}")
    
    try:
        # Read the temp file content
        with open(temp_file_path, 'rb') as f:
            extracted_content = f.read()
        
        print(f"📊 Extracted {len(extracted_content)} bytes")
        
        # Check if it starts with PDF magic number (clean extraction)
        if extracted_content.startswith(b'%PDF'):
            print("✅ SUCCESS: Clean PDF content extracted (starts with %PDF)")
            success = True
        else:
            print("❌ FAILURE: Content doesn't start with PDF magic number")
            print(f"First 100 bytes: {extracted_content[:100]}")
            success = False
        
        # Check for email header contamination
        if b'Return-Path:' in extracted_content or b'Received:' in extracted_content:
            print("❌ WARNING: Email headers still present in extracted content")
            success = False
        else:
            print("✅ SUCCESS: No email headers found in extracted content")
        
        return success
        
    finally:
        # Clean up temp file
        try:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                print(f"🧹 Cleaned up temp file: {temp_file_path}")
        except Exception as e:
            print(f"⚠️ Failed to clean up temp file: {e}")

def test_full_attachment_processing():
    """Test the full attachment processing with temp files"""

    print("\n🧪 Testing full attachment processing...")

    # Create test email
    email_data = create_test_email_with_attachment()

    # Create a minimal settings object for testing
    class MockSettings:
        def __init__(self):
            self.claims_email = "<EMAIL>"
            self.claims_email_password = "test_password"
            self.imap_server = "imap.example.com"
            self.imap_port = 993
            self.zendesk_token = "test_token"
            self.zendesk_subdomain = "test"
            self.zendesk_email = "<EMAIL>"
            self.supabase_url = "https://test.supabase.co"
            self.supabase_key = "test_key"

    # Initialize EmailMonitor with mock settings
    settings = MockSettings()
    email_monitor = EmailMonitor(settings=settings)
    
    # Test the full attachment extraction
    attachments = email_monitor.extract_attachments(email_data)
    
    if not attachments:
        print("❌ No attachments extracted")
        return False
    
    print(f"✅ Extracted {len(attachments)} attachment(s)")
    
    for i, attachment in enumerate(attachments):
        print(f"📎 Attachment {i+1}:")
        print(f"   Filename: {attachment.get('filename', 'Unknown')}")
        print(f"   Size: {attachment.get('size', 0)} bytes")
        print(f"   Content-Type: {attachment.get('content_type', 'Unknown')}")
        print(f"   Source: {attachment.get('source', 'Unknown')}")
        
        # Check the content
        content = attachment.get('content', b'')
        if content.startswith(b'%PDF'):
            print("   ✅ Clean PDF content (starts with %PDF)")
        else:
            print("   ❌ Content doesn't start with PDF magic number")
            print(f"   First 50 bytes: {content[:50]}")
        
        # Check for contamination
        if b'Return-Path:' in content or b'Received:' in content:
            print("   ❌ Email headers still present!")
            return False
        else:
            print("   ✅ No email headers found")
    
    return True

if __name__ == "__main__":
    print("🚀 Starting temp file extraction tests...\n")
    
    # Test 1: Direct temp file extraction
    test1_success = test_temp_file_extraction()
    
    # Test 2: Full attachment processing
    test2_success = test_full_attachment_processing()
    
    print(f"\n📊 Test Results:")
    print(f"   Temp file extraction: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"   Full processing: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! The temp file approach should fix the header contamination issue.")
    else:
        print("\n❌ Some tests failed. The issue may require further investigation.")
