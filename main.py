#!/usr/bin/env python3
"""
🏆 Zurich AI-Powered Claims Processing System
Main Application Entry Point

This is the revolutionary claims processing system that implements:
- Multi-model AI consensus (5 AI models)
- 6-model OCR consensus engine
- HumanLayer integration for human-in-the-loop workflows
- BAML structured AI outputs
- Canadian legal specialization
"""

import asyncio
import logging
import os
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, Request, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import structlog

# Import our core modules
from src.config.settings import Settings
from src.email_processing.email_monitor import EmailMonitor
from src.workflow_engine.workflow_coordinator import WorkflowCoordinator
from src.humanlayer_integration.approval_workflows import HumanLayerIntegration
from src.monitoring.performance_tracker import PerformanceTracker

# Configure structured logging for Dozzle compatibility
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Dozzle-compatible logging helper
def dozzle_log(level: str, message: str, **kwargs):
    """Enhanced logging for Dozzle visibility with both structured logs and print statements"""
    import datetime
    timestamp = datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")

    # Print statement for immediate Dozzle visibility
    if kwargs:
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        print(f"[{timestamp}] {level.upper()}: {message} | {extra_info}", flush=True)
    else:
        print(f"[{timestamp}] {level.upper()}: {message}", flush=True)

    # Structured log for detailed analysis
    if level.lower() == "info":
        logger.info(message, **kwargs)
    elif level.lower() == "error":
        logger.error(message, **kwargs)
    elif level.lower() == "warning":
        logger.warning(message, **kwargs)
    elif level.lower() == "debug":
        logger.debug(message, **kwargs)
    else:
        logger.info(message, **kwargs)

# Global application state
app_state = {
    "email_monitor": None,
    "workflow_coordinator": None,
    "humanlayer": None,
    "performance_tracker": None,
}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager - startup and shutdown"""

    dozzle_log("info", "🚀 [STARTUP] Starting Zurich AI Claims Processing System...")

    try:
        # Initialize settings
        dozzle_log("info", "⚙️ [STARTUP] Initializing settings...")
        settings = Settings()
        dozzle_log("info", "✅ [STARTUP] Settings initialized successfully")

        # Initialize core components
        dozzle_log("info", "🔧 [STARTUP] Initializing core components...")

        dozzle_log("info", "📊 [STARTUP] Creating PerformanceTracker...")
        app_state["performance_tracker"] = PerformanceTracker()
        dozzle_log("info", "✅ [STARTUP] PerformanceTracker created")

        dozzle_log("info", "🤝 [STARTUP] Creating HumanLayerIntegration...")
        app_state["humanlayer"] = HumanLayerIntegration(settings)
        dozzle_log("info", "✅ [STARTUP] HumanLayerIntegration created")

        dozzle_log("info", "🔄 [STARTUP] Creating WorkflowCoordinator...")
        app_state["workflow_coordinator"] = WorkflowCoordinator(
            settings=settings,
            performance_tracker=app_state["performance_tracker"]
        )
        dozzle_log("info", "✅ [STARTUP] WorkflowCoordinator created")

        dozzle_log("info", "📧 [STARTUP] Creating EmailMonitor...")
        email_config = {
            'claims_email': settings.claims_email,
            'claims_email_password': settings.claims_email_password,
            'imap_server': settings.imap_server,
            'imap_port': settings.imap_port
        }
        app_state["email_monitor"] = EmailMonitor(
            config=email_config,
            workflow_coordinator=app_state["workflow_coordinator"]
        )
        dozzle_log("info", "✅ [STARTUP] EmailMonitor created with IMAP configuration",
                   email=settings.claims_email,
                   server=settings.imap_server,
                   port=settings.imap_port)

        # Start background services
        dozzle_log("info", "🚀 [STARTUP] Starting background services...")

        dozzle_log("info", "📧 [STARTUP] Starting email monitoring service...")
        asyncio.create_task(app_state["email_monitor"].start_monitoring())
        dozzle_log("info", "✅ [STARTUP] Email monitoring task created")

        dozzle_log("info", "📊 [STARTUP] Starting performance monitoring...")
        asyncio.create_task(app_state["performance_tracker"].start_monitoring())
        dozzle_log("info", "✅ [STARTUP] Performance monitoring task created")

        dozzle_log("info", "🎉 [STARTUP] Zurich AI Claims Processing System started successfully!")
        dozzle_log("info", "🔍 [STARTUP] Application state summary",
                   email_monitor="initialized" if app_state["email_monitor"] else "not_initialized",
                   workflow_coordinator="initialized" if app_state["workflow_coordinator"] else "not_initialized",
                   humanlayer="initialized" if app_state["humanlayer"] else "not_initialized",
                   performance_tracker="initialized" if app_state["performance_tracker"] else "not_initialized")

        yield

    except Exception as e:
        dozzle_log("error", "❌ [STARTUP] Failed to start application", error=str(e))
        raise
    finally:
        # Cleanup on shutdown
        dozzle_log("info", "🛑 [SHUTDOWN] Shutting down Zurich AI Claims Processing System...")

        if app_state["email_monitor"]:
            dozzle_log("info", "📧 [SHUTDOWN] Stopping email monitor...")
            await app_state["email_monitor"].stop_monitoring()
            dozzle_log("info", "✅ [SHUTDOWN] Email monitor stopped")

        if app_state["performance_tracker"]:
            dozzle_log("info", "📊 [SHUTDOWN] Stopping performance tracker...")
            await app_state["performance_tracker"].stop_monitoring()
            dozzle_log("info", "✅ [SHUTDOWN] Performance tracker stopped")

        dozzle_log("info", "✅ [SHUTDOWN] Shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="Zurich AI Claims Processing System",
    description="Revolutionary AI-powered insurance claims processing with human-in-the-loop workflows",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "http://localhost:3000").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint - system status"""
    return {
        "message": "🏆 Zurich AI Claims Processing System",
        "status": "operational",
        "version": "1.0.0",
        "features": [
            "Multi-model AI consensus (5 AI models)",
            "6-model OCR consensus engine",
            "HumanLayer human-in-the-loop workflows",
            "BAML structured AI outputs",
            "Canadian legal specialization",
            "Real-time monitoring and analytics"
        ]
    }


@app.get("/health")
async def health_check():
    """Health check endpoint for Docker and monitoring"""
    import datetime
    return {
        "status": "healthy",
        "timestamp": datetime.datetime.utcnow().isoformat() + "Z",
        "version": "1.0.0",
        "services": {
            "api": "operational",
            "ai_models": "ready",
            "humanlayer": "configured",
            "baml": "generated"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check core components
        health_status = {
            "status": "healthy",
            "timestamp": structlog.processors.TimeStamper(fmt="iso")(),
            "components": {
                "email_monitor": "healthy" if app_state["email_monitor"] else "not_initialized",
                "workflow_coordinator": "healthy" if app_state["workflow_coordinator"] else "not_initialized",
                "humanlayer": "healthy" if app_state["humanlayer"] else "not_initialized",
                "performance_tracker": "healthy" if app_state["performance_tracker"] else "not_initialized",
            }
        }
        
        # Check if any component is unhealthy
        if any(status != "healthy" for status in health_status["components"].values()):
            health_status["status"] = "degraded"
        
        return health_status
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return JSONResponse(
            status_code=503,
            content={"status": "unhealthy", "error": str(e)}
        )


@app.post("/api/humanlayer/webhook")
async def humanlayer_webhook(request: Request, background_tasks: BackgroundTasks):
    """
    HumanLayer webhook endpoint for incoming emails
    This receives emails <NAME_EMAIL>
    """
    dozzle_log("info", "🌐 [WEBHOOK] Received HumanLayer webhook request",
               method=request.method, url=str(request.url))

    try:
        # Check if components are initialized
        dozzle_log("info", "🔧 [WEBHOOK] Checking component initialization...")
        if not app_state["humanlayer"]:
            dozzle_log("error", "❌ [WEBHOOK] HumanLayer integration not initialized")
            raise HTTPException(status_code=503, detail="HumanLayer integration not initialized")

        if not app_state["workflow_coordinator"]:
            dozzle_log("error", "❌ [WEBHOOK] Workflow coordinator not initialized")
            raise HTTPException(status_code=503, detail="Workflow coordinator not initialized")

        dozzle_log("info", "✅ [WEBHOOK] All components are initialized")

        # Get webhook signature and body
        dozzle_log("info", "🔐 [WEBHOOK] Processing webhook signature...")
        signature = request.headers.get("x-humanlayer-signature")
        body = await request.body()

        dozzle_log("info", "🔍 [WEBHOOK] Signature details",
                   has_signature=signature is not None,
                   body_length=len(body))

        # 🔍 DEBUG: Log raw webhook body for troubleshooting
        dozzle_log("info", "🔍 [WEBHOOK_DEBUG] Raw webhook body preview",
                   body_preview=body[:1000].decode('utf-8', errors='ignore') if body else "empty")

        # Verify webhook signature (Note: Agent webhooks don't require signature verification)
        dozzle_log("info", "🔐 [WEBHOOK] Verifying webhook signature...")
        signature_valid = app_state["humanlayer"].verify_webhook_signature(signature, body)
        dozzle_log("info", "✅ [WEBHOOK] Signature verification result", valid=signature_valid)

        if not signature_valid:
            dozzle_log("error", "❌ [WEBHOOK] Invalid webhook signature")
            raise HTTPException(status_code=401, detail="Invalid webhook signature")

        # Parse webhook data
        dozzle_log("info", "📄 [WEBHOOK] Parsing webhook JSON data...")
        data = await request.json()

        # 🔍 DEBUG: Log complete webhook data structure
        dozzle_log("info", "🔍 [WEBHOOK_DEBUG] Complete webhook data structure",
                   data_keys=list(data.keys()),
                   data_type=type(data).__name__)
        
        # Log the entire data structure for debugging (truncated for safety)
        import json
        data_json = json.dumps(data, indent=2, default=str)
        if len(data_json) > 5000:
            data_preview = data_json[:5000] + "... [TRUNCATED]"
        else:
            data_preview = data_json
        dozzle_log("info", "🔍 [WEBHOOK_DEBUG] Complete webhook JSON data", data_json=data_preview)

        dozzle_log("info", "📧 [WEBHOOK] Received HumanLayer webhook",
                   event_type=data.get("type"),
                   is_test=data.get("is_test", False))

        # Enhanced email details logging
        if data.get("event"):
            event = data["event"]
            
            # 🔍 DEBUG: Log complete event structure according to HumanLayer format
            dozzle_log("info", "🔍 [WEBHOOK_DEBUG] HumanLayer event data analysis",
                       event_keys=list(event.keys()),
                       event_type=type(event).__name__)
            
            # Log HumanLayer fields individually for clarity
            dozzle_log("info", "📬 [WEBHOOK_DEBUG] HumanLayer event field analysis",
                       from_address=event.get("from_address"),
                       to_address=event.get("to_address"),
                       subject=event.get("subject"),
                       message_id=event.get("message_id"),
                       body_length=len(event.get("body", "")),
                       body_type=type(event.get("body")).__name__,
                       raw_email_length=len(event.get("raw_email", "")),
                       previous_thread_count=len(event.get("previous_thread", [])))
            
            # Log full body content for debugging (check for corruption)
            email_body = event.get("body", "")
            if email_body:
                # Log first 2000 characters to see full content
                body_preview = email_body[:2000] if len(email_body) > 2000 else email_body
                dozzle_log("info", "📝 [WEBHOOK_DEBUG] HumanLayer email body content",
                           body_full=body_preview,
                           body_length=len(email_body),
                           body_encoding_check="utf-8" if email_body.isascii() else "non-ascii")
            
            # HumanLayer attachment analysis (from raw email)
            raw_email = event.get("raw_email", "")
            if raw_email:
                # Check for attachment indicators in raw email
                attachment_indicators = [
                    "Content-Disposition: attachment",
                    "Content-Type: application/", 
                    "filename=",
                    "Content-Transfer-Encoding: base64"
                ]
                has_attachment_indicators = any(indicator in raw_email for indicator in attachment_indicators)
                
                dozzle_log("info", "📎 [WEBHOOK_DEBUG] HumanLayer attachment analysis from raw email",
                           raw_email_length=len(raw_email),
                           has_attachment_indicators=has_attachment_indicators,
                           raw_email_preview=raw_email[:500] + "..." if len(raw_email) > 500 else raw_email)
            
            # Traditional logging (for compatibility)
            dozzle_log("info", "📬 [WEBHOOK] HumanLayer email details",
                       from_address=event.get("from_address"),
                       to_address=event.get("to_address"),
                       subject=event.get("subject"),
                       message_id=event.get("message_id"),
                       body_length=len(event.get("body", "")),
                       webhook_type=data.get("type"),
                       is_test=data.get("is_test", False))

        # Process email in background
        dozzle_log("info", "🚀 [WEBHOOK] Starting background email processing...")
        background_tasks.add_task(
            app_state["workflow_coordinator"].process_incoming_email,
            data
        )
        dozzle_log("info", "✅ [WEBHOOK] Background task added successfully")

        response = {"status": "received", "message": "Email processing started"}
        dozzle_log("info", "📤 [WEBHOOK] Sending response", status=response["status"])
        return response

    except HTTPException as he:
        dozzle_log("error", "❌ [WEBHOOK] HTTP exception occurred",
                   status_code=he.status_code, detail=he.detail)
        raise
    except Exception as e:
        dozzle_log("error", "❌ [WEBHOOK] Unexpected error in webhook processing",
                   error=str(e), error_type=type(e).__name__)
        raise HTTPException(status_code=500, detail=f"Webhook processing failed: {str(e)}")


@app.post("/api/v1/email/process")
async def process_email(email_data: Dict[str, Any], background_tasks: BackgroundTasks):
    """Process email through AI classification pipeline"""
    dozzle_log("info", "📧 [EMAIL_API] Received email processing request")

    try:
        # Check if workflow coordinator is initialized
        dozzle_log("info", "🔧 [EMAIL_API] Checking workflow coordinator...")
        if not app_state["workflow_coordinator"]:
            dozzle_log("error", "❌ [EMAIL_API] Workflow coordinator not initialized")
            raise HTTPException(status_code=503, detail="Workflow coordinator not initialized")

        dozzle_log("info", "✅ [EMAIL_API] Workflow coordinator is available")

        # Extract email details
        subject = email_data.get("subject", "")
        body = email_data.get("body", "")
        sender_email = email_data.get("sender_email", "")
        attachments = email_data.get("attachments", [])

        dozzle_log("info", "📧 [EMAIL_API] Processing email",
                   subject=subject[:50] + "..." if len(subject) > 50 else subject,
                   sender=sender_email,
                   body_length=len(body),
                   attachments_count=len(attachments))

        # Create email data for direct processing (no HumanLayer webhook format needed)
        email_data = {
            "from_address": sender_email,
            "to_address": "<EMAIL>",
            "subject": subject,
            "body": body,
            "message_id": f"<api-{hash(subject + body + sender_email)}@api.local>",
            "attachments": attachments,
            "received_at": datetime.now().isoformat() + "Z",
            "source": "api"
        }

        # Process email in background
        dozzle_log("info", "🚀 [EMAIL_API] Starting background email processing...")
        background_tasks.add_task(
            app_state["workflow_coordinator"].process_incoming_email,
            email_data
        )
        dozzle_log("info", "✅ [EMAIL_API] Background task added successfully")

        response = {
            "status": "received",
            "message": "Email processing started",
            "subject": subject,
            "sender": sender_email
        }
        dozzle_log("info", "📤 [EMAIL_API] Sending response", status=response["status"])
        return response

    except HTTPException as he:
        dozzle_log("error", "❌ [EMAIL_API] HTTP exception occurred",
                   status_code=he.status_code, detail=he.detail)
        raise
    except Exception as e:
        dozzle_log("error", "❌ [EMAIL_API] Unexpected error in email processing",
                   error=str(e), error_type=type(e).__name__)
        raise HTTPException(status_code=500, detail=f"Email processing failed: {str(e)}")


@app.post("/api/claims/manual")
async def manual_claim_submission(claim_data: Dict[str, Any], background_tasks: BackgroundTasks):
    """Manual claim submission endpoint (for testing or direct submissions)"""
    dozzle_log("info", "📝 [MANUAL_CLAIM] Received manual claim submission request")

    try:
        # Check if workflow coordinator is initialized
        dozzle_log("info", "🔧 [MANUAL_CLAIM] Checking workflow coordinator...")
        if not app_state["workflow_coordinator"]:
            dozzle_log("error", "❌ [MANUAL_CLAIM] Workflow coordinator not initialized")
            raise HTTPException(status_code=503, detail="Workflow coordinator not initialized")

        dozzle_log("info", "✅ [MANUAL_CLAIM] Workflow coordinator is available")

        # Log claim details
        claim_id = claim_data.get("claim_id")
        dozzle_log("info", "📝 [MANUAL_CLAIM] Processing claim submission",
                   claim_id=claim_id,
                   customer_name=claim_data.get("customer_name"),
                   policy_number=claim_data.get("policy_number"),
                   incident_type=claim_data.get("incident_type"),
                   estimated_damage=claim_data.get("estimated_damage"))

        # Process claim in background
        dozzle_log("info", "🚀 [MANUAL_CLAIM] Starting background claim processing...")
        background_tasks.add_task(
            app_state["workflow_coordinator"].process_manual_claim,
            claim_data
        )
        dozzle_log("info", "✅ [MANUAL_CLAIM] Background task added successfully")

        response = {
            "status": "received",
            "claim_id": claim_id,
            "message": "Claim processing started"
        }
        dozzle_log("info", "📤 [MANUAL_CLAIM] Sending response",
                   status=response["status"], claim_id=claim_id)
        return response

    except HTTPException as he:
        dozzle_log("error", "❌ [MANUAL_CLAIM] HTTP exception occurred",
                   status_code=he.status_code, detail=he.detail)
        raise
    except Exception as e:
        dozzle_log("error", "❌ [MANUAL_CLAIM] Unexpected error in claim submission",
                   error=str(e), error_type=type(e).__name__)
        raise HTTPException(status_code=500, detail=f"Claim submission failed: {str(e)}")


@app.get("/api/claims/{claim_id}/status")
async def get_claim_status(claim_id: str):
    """Get claim processing status"""
    dozzle_log("info", "🔍 [CLAIM_STATUS] Received claim status request", claim_id=claim_id)

    try:
        # Check if workflow coordinator is initialized
        dozzle_log("info", "🔧 [CLAIM_STATUS] Checking workflow coordinator...")
        if not app_state["workflow_coordinator"]:
            dozzle_log("error", "❌ [CLAIM_STATUS] Workflow coordinator not initialized")
            raise HTTPException(status_code=503, detail="Workflow coordinator not initialized")

        dozzle_log("info", "✅ [CLAIM_STATUS] Workflow coordinator is available")

        # Get claim status
        dozzle_log("info", "🔍 [CLAIM_STATUS] Fetching claim status from workflow coordinator...")
        status = await app_state["workflow_coordinator"].get_claim_status(claim_id)

        if not status:
            dozzle_log("warning", "⚠️ [CLAIM_STATUS] Claim not found", claim_id=claim_id)
            raise HTTPException(status_code=404, detail="Claim not found")

        dozzle_log("info", "✅ [CLAIM_STATUS] Claim status retrieved successfully",
                   claim_id=claim_id,
                   workflow_id=status.get("workflow_id"),
                   status_value=status.get("status"),
                   workflow_type=status.get("type"))

        dozzle_log("info", "📤 [CLAIM_STATUS] Sending status response",
                   claim_id=claim_id, status=status.get("status"))
        return status

    except HTTPException as he:
        dozzle_log("error", "❌ [CLAIM_STATUS] HTTP exception occurred",
                   claim_id=claim_id, status_code=he.status_code, detail=he.detail)
        raise
    except Exception as e:
        dozzle_log("error", "❌ [CLAIM_STATUS] Unexpected error getting claim status",
                   claim_id=claim_id, error=str(e), error_type=type(e).__name__)
        raise HTTPException(status_code=500, detail=f"Failed to get claim status: {str(e)}")


@app.get("/api/claims/track/{claim_reference}")
async def track_claim(claim_reference: str):
    """
    Track claim by reference number for customer-facing tracking page
    
    Args:
        claim_reference: Claim reference number (e.g., PI12345678)
        
    Returns:
        JSON response with claim details and business status
    """
    try:
        dozzle_log("info", "📡 [TRACK_CLAIM] Claim tracking request received",
                  claim_reference=claim_reference)
        
        # Validate claim reference format
        if not claim_reference or len(claim_reference) < 6:
            dozzle_log("warning", "⚠️ [TRACK_CLAIM] Invalid claim reference format",
                      claim_reference=claim_reference)
            return JSONResponse(
                content={
                    'error': 'Invalid claim reference format',
                    'message': 'Please provide a valid claim reference number'
                },
                status_code=400
            )
        
        # Return demo tracking data (can be connected to database later)
        tracking_data = {
            "claim_reference": claim_reference,
            "business_status": "Under Initial Review",
            "business_status_description": "Your claim is being reviewed by our claims team. We will contact you within 2-3 business days.",
            "claim_type": "Personal Injury",
            "incident_date": "2024-01-15",
            "submitted_date": "2024-01-16T10:30:00Z",
            "last_updated": "2024-01-20T14:15:00Z",
            "assigned_executive": "Claims Specialist Team",
            "estimated_completion": "5-7 business days from submission",
            "current_step": 2,
            "total_steps": 5,
            "progress_percentage": 40,
            "contact_info": {
                "phone": "******-ZURICH",
                "email": "<EMAIL>",
                "hours": "Monday-Friday 8AM-6PM EST"
            },
            "timeline": [
                {
                    "step": 1,
                    "title": "Claim Received",
                    "description": "Your claim has been received and logged in our system.",
                    "status": "completed",
                    "timestamp": "2024-01-16T10:30:00Z"
                },
                {
                    "step": 2,
                    "title": "Under Initial Review",
                    "description": "Our claims team is reviewing your submission and documentation.",
                    "status": "in_progress",
                    "timestamp": "2024-01-16T11:00:00Z"
                },
                {
                    "step": 3,
                    "title": "Documentation Review",
                    "description": "Reviewing all submitted documents and evidence.",
                    "status": "pending",
                    "timestamp": None
                },
                {
                    "step": 4,
                    "title": "Assessment & Investigation",
                    "description": "Detailed assessment and investigation of your claim.",
                    "status": "pending",
                    "timestamp": None
                },
                {
                    "step": 5,
                    "title": "Resolution & Settlement",
                    "description": "Final resolution and settlement processing.",
                    "status": "pending",
                    "timestamp": None
                }
            ],
            "documents": [
                {
                    "name": "Incident Report",
                    "type": "pdf",
                    "uploaded_date": "2024-01-16T10:30:00Z",
                    "status": "processed"
                },
                {
                    "name": "Medical Report",
                    "type": "pdf", 
                    "uploaded_date": "2024-01-16T10:32:00Z",
                    "status": "under_review"
                }
            ]
        }
        
        dozzle_log("info", "✅ [TRACK_CLAIM] Claim tracking data retrieved successfully",
                  claim_reference=claim_reference,
                  status=tracking_data.get('business_status'))
        
        return JSONResponse(content=tracking_data)
        
    except Exception as e:
        dozzle_log("error", "❌ [TRACK_CLAIM] Error retrieving claim tracking data",
                  claim_reference=claim_reference,
                  error=str(e))
        return JSONResponse(
            content={
                'error': 'Internal server error',
                'message': 'Unable to retrieve claim information at this time'
            },
            status_code=500
        )


@app.get("/api/metrics")
async def get_metrics():
    """Get system performance metrics"""
    try:
        if not app_state["performance_tracker"]:
            raise HTTPException(status_code=503, detail="Performance tracker not initialized")
        
        metrics = await app_state["performance_tracker"].get_current_metrics()
        return metrics
        
    except Exception as e:
        logger.error("❌ Failed to get metrics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")


@app.get("/api/email/status")
async def get_email_monitor_status():
    """Get email monitor status and statistics"""
    dozzle_log("info", "📧 [EMAIL_STATUS] Email monitor status requested")
    
    try:
        if not app_state["email_monitor"]:
            return {"error": "Email monitor not initialized"}
        
        status = app_state["email_monitor"].get_status()
        dozzle_log("info", "📧 [EMAIL_STATUS] Status retrieved successfully", status=status["status"])
        return status
        
    except Exception as e:
        dozzle_log("error", "❌ [EMAIL_STATUS] Failed to get email monitor status", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get email monitor status: {str(e)}")


@app.post("/api/email/test-connection")
async def test_email_connection():
    """Test IMAP connection to email server"""
    dozzle_log("info", "🔧 [EMAIL_TEST] Testing email connection...")
    
    try:
        if not app_state["email_monitor"]:
            return {"error": "Email monitor not initialized"}
        
        connection_test = await app_state["email_monitor"].test_connection()
        
        if connection_test:
            dozzle_log("info", "✅ [EMAIL_TEST] Email connection test successful")
            return {"status": "success", "message": "IMAP connection successful"}
        else:
            dozzle_log("error", "❌ [EMAIL_TEST] Email connection test failed")
            return {"status": "failed", "message": "IMAP connection failed"}
        
    except Exception as e:
        dozzle_log("error", "❌ [EMAIL_TEST] Email connection test error", error=str(e))
        raise HTTPException(status_code=500, detail=f"Connection test failed: {str(e)}")


@app.get("/api/email/recent")
async def get_recent_emails(limit: int = 5):
    """Get recent emails from inbox (for testing)"""
    dozzle_log("info", "📬 [EMAIL_RECENT] Getting recent emails", limit=limit)
    
    try:
        if not app_state["email_monitor"]:
            return {"error": "Email monitor not initialized"}
        
        emails = await app_state["email_monitor"].get_recent_emails(limit=limit)
        
        # Don't log full email content for privacy
        email_summaries = []
        for email in emails:
            summary = {
                "subject": email.get("subject", "No subject"),
                "from_address": email.get("from_address", "Unknown"),
                "received_at": email.get("received_at", "Unknown"),
                "has_attachments": email.get("has_attachments", False),
                "body_length": len(email.get("body", "")),
                "source": email.get("source", "unknown")
            }
            email_summaries.append(summary)
        
        dozzle_log("info", "📬 [EMAIL_RECENT] Retrieved recent emails", count=len(emails))
        return {
            "status": "success",
            "count": len(emails),
            "emails": email_summaries
        }
        
    except Exception as e:
        dozzle_log("error", "❌ [EMAIL_RECENT] Failed to get recent emails", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get recent emails: {str(e)}")


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    dozzle_log("error", "💥 [GLOBAL_EXCEPTION] Unhandled exception caught",
               path=request.url.path,
               method=request.method,
               error=str(exc),
               error_type=type(exc).__name__)

    # Log request body if available (for debugging)
    try:
        if hasattr(request, '_body'):
            body = await request.body()
            if body:
                dozzle_log("error", "📄 [GLOBAL_EXCEPTION] Request body",
                           body_length=len(body),
                           body_preview=body.decode('utf-8', errors='ignore')[:200])
    except Exception as body_error:
        dozzle_log("error", "❌ [GLOBAL_EXCEPTION] Could not read request body",
                   body_error=str(body_error))

    response_content = {
        "error": "Internal server error",
        "message": "An unexpected error occurred",
        "path": request.url.path,
        "error_type": type(exc).__name__
    }

    dozzle_log("error", "📤 [GLOBAL_EXCEPTION] Sending error response",
               error_type=response_content["error_type"])

    return JSONResponse(
        status_code=500,
        content=response_content
    )


if __name__ == "__main__":
    """Run the application directly"""
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Configure logging for direct run
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Get configuration from environment
    host = os.getenv("APP_HOST", "0.0.0.0")
    port = int(os.getenv("APP_PORT", 8000))
    debug = os.getenv("APP_DEBUG", "false").lower() == "true"
    
    logger.info(f"🚀 Starting Zurich AI Claims Processing System on {host}:{port}")
    
    # Run the application
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if not debug else "debug"
    )
