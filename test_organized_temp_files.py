#!/usr/bin/env python3
"""
Test the new organized temp file approach: tmp/claimid/files/
This should completely eliminate email header contamination
"""

import sys
import os
import tempfile
import logging
import uuid
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from email_processing.email_monitor import EmailMonitor

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_email_with_multiple_attachments():
    """Create a test email with multiple attachments (properly encoded)"""

    # Create a multipart message
    msg = MIMEMultipart()
    msg['Subject'] = 'Test Claim with Multiple Documents'
    msg['From'] = '<EMAIL>'
    msg['To'] = '<EMAIL>'

    # Add email body
    from email.mime.text import MIMEText
    body_text = "Please find attached documents for my insurance claim."
    msg.attach(MIMEText(body_text, 'plain'))

    # Create multiple test documents (clean content)
    test_files = [
        {
            'filename': 'accident_report.pdf',
            'content': b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\nACCIDENT REPORT CONTENT',
            'content_type': 'application/pdf'
        },
        {
            'filename': 'medical_records.pdf',
            'content': b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\nMEDICAL RECORDS CONTENT',
            'content_type': 'application/pdf'
        },
        {
            'filename': 'damage_photo.jpg',
            'content': b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00DAMAGE PHOTO DATA',
            'content_type': 'image/jpeg'
        }
    ]

    # Add clean attachments (this simulates proper email encoding)
    for file_info in test_files:
        # Create attachment part with clean content
        attachment = MIMEBase('application', 'octet-stream')
        attachment.set_payload(file_info['content'])  # Clean content only
        encoders.encode_base64(attachment)
        attachment.add_header(
            'Content-Disposition',
            f'attachment; filename="{file_info["filename"]}"'
        )
        attachment.add_header('Content-Type', file_info['content_type'])

        msg.attach(attachment)

    return msg.as_bytes()

def create_contaminated_email_simulation():
    """
    Create an email that simulates the contamination issue we've been seeing
    This represents what happens when email parsers mix headers with content
    """

    # This simulates a raw email message where headers got mixed with attachment content
    raw_email = b"""Return-Path: <<EMAIL>>
Received: from mail.example.com by claims.zurich.com
Subject: Insurance Claim Documents
From: <EMAIL>
To: <EMAIL>
Content-Type: multipart/mixed; boundary="boundary123"

--boundary123
Content-Type: text/plain

Please find attached documents for my insurance claim.

--boundary123
Content-Type: application/pdf
Content-Disposition: attachment; filename="contaminated_report.pdf"
Content-Transfer-Encoding: base64

""" + b"""************************************************************************************************************************************************************************************************************************************************************************************""" + b"""

--boundary123--
"""

    return raw_email

async def test_organized_temp_file_extraction():
    """Test the new organized temp file extraction approach"""

    print("🧪 Testing organized temp file extraction (tmp/claimid/files/)...")

    # Test 1: Clean email attachments (properly encoded)
    print("\n📧 Test 1: Clean email attachments...")
    email_data = create_test_email_with_multiple_attachments()
    success1 = await _test_email_extraction(email_data, "clean")

    # Test 2: Contaminated email simulation
    print("\n📧 Test 2: Contaminated email simulation...")
    contaminated_email_data = create_contaminated_email_simulation()
    success2 = await _test_email_extraction(contaminated_email_data, "contaminated")

    return success1 and success2

async def _test_email_extraction(email_data, test_type):
    """Helper function to test email extraction"""

    print(f"   Testing {test_type} email extraction...")

    # Create a minimal settings object for testing
    class MockSettings:
        def __init__(self):
            self.claims_email = "<EMAIL>"
            self.claims_email_password = "test_password"
            self.imap_server = "imap.zurich.com"
            self.imap_port = 993
            self.zendesk_token = "test_token"
            self.zendesk_subdomain = "zurich"
            self.zendesk_email = "<EMAIL>"
            self.supabase_url = "https://test.supabase.co"
            self.supabase_key = "test_key"

    # Initialize EmailMonitor with mock settings
    settings = MockSettings()
    email_monitor = EmailMonitor(settings=settings)

    # Parse email message
    import email
    from email.policy import default
    msg = email.message_from_bytes(email_data, policy=default)

    # Extract attachments using the new organized approach
    attachments = await email_monitor._extract_attachments(msg)
    
    if not attachments:
        print(f"   ❌ No attachments extracted for {test_type} email")
        return False

    print(f"   ✅ Extracted {len(attachments)} attachment(s) from {test_type} email")

    # Verify each attachment
    success = True
    claim_id = None

    for attachment in attachments:
        filename = attachment.get('filename', 'Unknown')
        size = attachment.get('size', 0)
        temp_file_path = attachment.get('temp_file_path')
        attachment_claim_id = attachment.get('claim_id')

        print(f"      📎 {filename}: {size} bytes")

        # Store claim ID for later cleanup
        if not claim_id:
            claim_id = attachment_claim_id

        # Verify temp file exists and is organized correctly
        if temp_file_path and os.path.exists(temp_file_path):
            # Check if path follows tmp/claimid/files/ structure
            expected_pattern = f"{tempfile.gettempdir()}/{attachment_claim_id}/files/"
            if expected_pattern in temp_file_path:
                print(f"         ✅ Organized structure correct")
            else:
                print(f"         ❌ Wrong structure: {temp_file_path}")
                success = False

            # Verify file content is clean (no email headers)
            with open(temp_file_path, 'rb') as f:
                file_content = f.read()

            # For contaminated test, we expect headers to be present (showing the problem)
            # For clean test, we expect no headers
            has_headers = b'Return-Path:' in file_content or b'Received:' in file_content

            if test_type == "contaminated":
                if has_headers:
                    print(f"         ⚠️ Headers present (expected for contaminated test)")
                else:
                    print(f"         ✅ No headers (contamination was cleaned!)")
            else:  # clean test
                if has_headers:
                    print(f"         ❌ Unexpected headers in clean email!")
                    success = False
                else:
                    print(f"         ✅ No headers (as expected)")

            # Check for expected content based on file type
            if filename.endswith('.pdf') and file_content.startswith(b'%PDF'):
                print(f"         ✅ Valid PDF content")
            elif filename.endswith('.jpg') and file_content.startswith(b'\xff\xd8'):
                print(f"         ✅ Valid JPEG content")
            else:
                print(f"         ⚠️ Content check inconclusive")
        else:
            print(f"         ❌ Temp file not found: {temp_file_path}")
            success = False

    # Clean up after test
    if claim_id:
        email_monitor.cleanup_claim_temp_files(claim_id)

    return success

async def main():
    print("🚀 Starting organized temp file tests (tmp/claimid/files/)...\n")

    test_success = await test_organized_temp_file_extraction()

    print(f"\n📊 Test Results:")
    print(f"   Organized temp file approach: {'✅ PASS' if test_success else '❌ FAIL'}")

    if test_success:
        print("\n🎉 SUCCESS! The organized temp file approach (tmp/claimid/files/) should completely")
        print("   eliminate email header contamination and provide clean files for OCR processing.")
        print("\n💡 Next steps:")
        print("   1. Upload clean files from temp to Supabase storage")
        print("   2. Use temp files directly for OCR API with form data")
        print("   3. Clean up temp files after successful processing")
    else:
        print("\n❌ FAILURE! The organized temp file approach needs further investigation.")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
