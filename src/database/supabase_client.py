"""
🗄️ Supabase Client for Zurich Claims Processing

Provides database operations and file storage for:
- Claims data management
- Attachment storage with organized folder structure
- Secure file access with signed URLs
- Comprehensive error handling and retry logic
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path
import uuid
import json

from supabase import create_client, Client
from supabase.lib.client_options import ClientOptions
import structlog

from ..config.settings import Settings
from ..utils.dozzle_logger import dozzle_log

logger = structlog.get_logger(__name__)


class SupabaseClient:
    """
    Comprehensive Supabase client for claims processing
    
    Features:
    - Database operations with retry logic
    - File storage with organized folder structure
    - Signed URL generation for secure file access
    - Comprehensive error handling and logging
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client: Optional[Client] = None
        self.storage_bucket = "claims-attachments"
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Supabase client with proper configuration"""
        try:
            # Use service role key for full database access (bypasses RLS)
            self.client = create_client(
                supabase_url=self.settings.supabase_url,
                supabase_key=self.settings.supabase_service_role_key,
                options=ClientOptions(
                    auto_refresh_token=False,  # Service role doesn't need token refresh
                    persist_session=False     # Service role doesn't need session persistence
                )
            )
            
            dozzle_log("info", "✅ [SUPABASE] Client initialized successfully",
                      url=self.settings.supabase_url,
                      bucket=self.storage_bucket)
            
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to initialize client",
                      error=str(e),
                      url=self.settings.supabase_url)
            raise
    
    async def create_claim(self, claim_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new claim record in Supabase

        Args:
            claim_data: Dictionary containing claim information

        Returns:
            Created claim record with id (not claim_id)
        """
        try:
            # Validate required fields
            required_fields = ['workflow_id', 'sender_email']
            for field in required_fields:
                if not claim_data.get(field):
                    raise ValueError(f"Required field '{field}' is missing or empty")
            
            # Remove claim_id if present (database uses 'id' as primary key)
            if 'claim_id' in claim_data:
                del claim_data['claim_id']

            # Set timestamps (let database handle created_at/updated_at with defaults)
            # Remove manual timestamp setting to use database defaults
            if 'created_at' in claim_data:
                del claim_data['created_at']
            if 'updated_at' in claim_data:
                del claim_data['updated_at']

            # Clean sender email (remove display names, validate format)
            sender_email = claim_data.get('sender_email', '').strip()
            if '<' in sender_email and '>' in sender_email:
                # <AUTHOR> <EMAIL>" format
                import re
                email_match = re.search(r'<([^>]+)>', sender_email)
                if email_match:
                    sender_email = email_match.group(1).strip()
            claim_data['sender_email'] = sender_email

            dozzle_log("info", "📝 [SUPABASE] Creating claim record",
                      workflow_id=claim_data.get('workflow_id'),
                      sender_email=sender_email)

            # Insert claim record
            result = self.client.table('claims').insert(claim_data).execute()

            if result.data:
                created_claim = result.data[0]
                dozzle_log("info", "✅ [SUPABASE] Claim created successfully",
                          claim_id=created_claim['id'],  # Database uses 'id' field
                          workflow_id=created_claim['workflow_id'])
                return created_claim
            else:
                raise Exception("No data returned from insert operation")

        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to create claim",
                      workflow_id=claim_data.get('workflow_id'),
                      error=str(e))
            raise
    
    async def update_claim(self, claim_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing claim record
        
        Args:
            claim_id: UUID of the claim to update
            updates: Dictionary of fields to update
            
        Returns:
            Updated claim record
        """
        try:
            # Add updated timestamp
            updates['updated_at'] = datetime.utcnow().isoformat()
            
            dozzle_log("info", "📝 [SUPABASE] Updating claim record",
                      claim_id=claim_id,
                      fields=list(updates.keys()))
            
            result = self.client.table('claims').update(updates).eq('id', claim_id).execute()
            
            if result.data:
                updated_claim = result.data[0]
                dozzle_log("info", "✅ [SUPABASE] Claim updated successfully",
                          claim_id=claim_id)
                return updated_claim
            else:
                raise Exception(f"Claim not found: {claim_id}")
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to update claim",
                      claim_id=claim_id,
                      error=str(e))
            raise
    
    async def update_claim_status(self, claim_id: str, new_status: str, notes: Optional[str] = None) -> bool:
        """
        Update claim status with optional notes
        
        Args:
            claim_id: UUID of the claim to update
            new_status: New status value
            notes: Optional notes about the status change
            
        Returns:
            True if update successful, False otherwise
        """
        try:
            dozzle_log("info", "📝 [SUPABASE] Updating claim status",
                      claim_id=claim_id,
                      new_status=new_status,
                      has_notes=bool(notes))
            
            # Prepare update data
            updates = {
                'workflow_status': new_status,
                'updated_at': datetime.utcnow().isoformat()
            }
            
            # Add notes if provided
            if notes:
                updates['notes'] = notes
            
            # Update the claim
            result = self.client.table('claims').update(updates).eq('id', claim_id).execute()
            
            if result.data:
                dozzle_log("info", "✅ [SUPABASE] Claim status updated successfully",
                          claim_id=claim_id,
                          new_status=new_status)
                
                # Add to claim history for audit trail
                await self.add_claim_history(
                    claim_id=claim_id,
                    event_type="status_update",
                    description=f"Status updated to: {new_status}",
                    new_values={'status': new_status},
                    metadata={'notes': notes} if notes else None
                )
                
                return True
            else:
                dozzle_log("warning", "⚠️ [SUPABASE] Claim not found for status update",
                          claim_id=claim_id)
                return False
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to update claim status",
                      claim_id=claim_id,
                      new_status=new_status,
                      error=str(e))
            return False
    
    async def get_claim_by_workflow_id(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve claim by workflow_id
        
        Args:
            workflow_id: Workflow ID from email processing
            
        Returns:
            Claim record or None if not found
        """
        try:
            result = self.client.table('claims').select('*').eq('workflow_id', workflow_id).execute()
            
            if result.data:
                claim = result.data[0]
                dozzle_log("info", "✅ [SUPABASE] Claim retrieved successfully",
                          workflow_id=workflow_id,
                          claim_id=claim['id'])
                return claim
            else:
                dozzle_log("info", "ℹ️ [SUPABASE] Claim not found",
                          workflow_id=workflow_id)
                return None
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to retrieve claim",
                      workflow_id=workflow_id,
                      error=str(e))
            raise
    
    async def get_claim_by_reference(self, claim_reference: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve claim by claim reference number
        
        Args:
            claim_reference: Claim reference number (e.g., PI12345678)
            
        Returns:
            Claim record or None if not found
        """
        try:
            dozzle_log("info", "🔍 [SUPABASE] Searching for claim by reference",
                      claim_reference=claim_reference)
            
            # First try exact match on claim_reference field
            result = self.client.table('claims').select('*').eq('claim_reference', claim_reference).execute()
            
            if result.data:
                claim = result.data[0]
                dozzle_log("info", "✅ [SUPABASE] Claim found by reference",
                          claim_reference=claim_reference,
                          claim_id=claim['id'])
                return claim
            
            # If not found, try searching by partial ID match
            # Extract suffix from reference (e.g., "64CBF180" from "PI64CBF180")
            reference_suffix = claim_reference[2:] if len(claim_reference) > 2 else claim_reference
            
            # Search for claims where ID contains the reference suffix
            all_result = self.client.table('claims').select('*').execute()
            
            if all_result.data:
                for claim in all_result.data:
                    claim_id = claim.get('id', '')
                    if reference_suffix.lower() in claim_id.replace('-', '').lower():
                        dozzle_log("info", "✅ [SUPABASE] Claim found by ID partial match",
                                  claim_reference=claim_reference,
                                  claim_id=claim['id'])
                        return claim
            
            dozzle_log("info", "ℹ️ [SUPABASE] Claim not found",
                      claim_reference=claim_reference)
            return None
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to retrieve claim by reference",
                      claim_reference=claim_reference,
                      error=str(e))
            raise
    
    async def get_all_claims(self) -> List[Dict[str, Any]]:
        """
        Retrieve all claims (for reference matching)
        
        Returns:
            List of all claim records
        """
        try:
            dozzle_log("info", "📋 [SUPABASE] Retrieving all claims")
            
            result = self.client.table('claims').select('*').execute()
            
            if result.data:
                dozzle_log("info", "✅ [SUPABASE] All claims retrieved successfully",
                          count=len(result.data))
                return result.data
            else:
                dozzle_log("info", "ℹ️ [SUPABASE] No claims found")
                return []
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to retrieve all claims",
                      error=str(e))
            raise
    
    async def get_claim_attachments(self, claim_id: str) -> List[Dict[str, Any]]:
        """
        Retrieve all attachments for a specific claim
        
        Args:
            claim_id: UUID of the claim
            
        Returns:
            List of attachment records
        """
        try:
            dozzle_log("info", "📎 [SUPABASE] Retrieving claim attachments",
                      claim_id=claim_id)
            
            result = self.client.table('attachments').select('*').eq('claim_id', claim_id).execute()
            
            if result.data:
                dozzle_log("info", "✅ [SUPABASE] Claim attachments retrieved successfully",
                          claim_id=claim_id,
                          count=len(result.data))
                return result.data
            else:
                dozzle_log("info", "ℹ️ [SUPABASE] No attachments found for claim",
                          claim_id=claim_id)
                return []
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to retrieve claim attachments",
                      claim_id=claim_id,
                      error=str(e))
            raise
    
    def _generate_storage_path(self, claim_id: str, attachment_id: str, filename: str) -> str:
        """
        Generate simple storage path: claims/{claim_id}/{original_filename}
        
        Args:
            claim_id: UUID of the claim
            attachment_id: Not used in simple format
            filename: Original filename (preserved exactly)
            
        Returns:
            Simple storage path preserving original filename
        """
        # Keep original filename exactly as-is (no modifications)
        return f"claims/{claim_id}/{filename}"
    
    async def upload_attachment(self, 
                              claim_id: str,
                              workflow_id: str,
                              file_content: bytes,
                              filename: str,
                              content_type: str) -> Dict[str, Any]:
        """
        Upload attachment to Supabase Storage with simple structure
        
        Args:
            claim_id: UUID of the associated claim
            workflow_id: Workflow ID for organization
            file_content: Binary file content (clean, no email headers)
            filename: Original filename (preserved exactly)
            content_type: MIME type of the file
            
        Returns:
            Attachment record with storage information
        """
        try:
            # Generate unique attachment ID
            attachment_id = str(uuid.uuid4())
            
            # Generate simple storage path: claims/{claim_id}/{original_filename}
            storage_path = self._generate_storage_path(claim_id, attachment_id, filename)
            
            dozzle_log("info", "📎 [SUPABASE] Uploading attachment with simple structure",
                      claim_id=claim_id,
                      attachment_id=attachment_id,
                      original_filename=filename,
                      storage_path=storage_path,
                      size=len(file_content))
            
            # Upload file to Supabase Storage
            try:
                upload_result = self.client.storage.from_(self.storage_bucket).upload(
                    file=file_content,  # Clean binary content
                    path=storage_path,   # Simple path: claims/{claim_id}/{filename}
                    file_options={
                        "content-type": content_type,
                        "cache-control": "3600",
                        "upsert": "false"  # Don't overwrite existing files
                    }
                )
                
                dozzle_log("info", "✅ [SUPABASE] File uploaded to storage successfully",
                          storage_path=storage_path,
                          bucket=self.storage_bucket)
                
            except Exception as storage_error:
                dozzle_log("error", "❌ [SUPABASE] Storage upload failed",
                          storage_path=storage_path,
                          error=str(storage_error))
                raise Exception(f"Storage upload failed: {storage_error}")
            
            # Create attachment metadata record
            attachment_data = {
                'id': attachment_id,
                'workflow_id': workflow_id,
                'claim_id': claim_id,
                'original_filename': filename,  # Keep original filename exactly
                'storage_path': storage_path,   # Simple path
                'file_size': len(file_content),
                'content_type': content_type,
                'upload_status': 'uploaded',
                'filename': filename,
                'storage_bucket': self.storage_bucket,
                'status': 'uploaded',
                'attachment_id': attachment_id,
                'upload_metadata': {
                    'workflow_id': workflow_id,
                    'attachment_id': attachment_id,
                    'upload_timestamp': datetime.utcnow().isoformat(),
                    'content_type': content_type,
                    'file_size': len(file_content),
                    'storage_structure': 'simple_original_filename'
                },
                # OCR fields (will be populated later)
                'ocr_text': None,
                'ocr_confidence': None,
                'document_type': None,
                'processing_metadata': None,
                'processed_at': None
            }
            
            # Insert attachment record
            result = self.client.table('attachments').insert(attachment_data).execute()
            
            if result.data:
                attachment = result.data[0]
                
                # Generate signed URL for access
                signed_url = await self.get_signed_url(storage_path)
                if signed_url:
                    # Update attachment with signed URL
                    await self.update_attachment(attachment['id'], {
                        'storage_url': signed_url
                    })
                    attachment['storage_url'] = signed_url
                
                dozzle_log("info", "✅ [SUPABASE] Attachment uploaded and recorded successfully",
                          attachment_id=attachment['id'],
                          claim_id=claim_id,
                          original_filename=filename,
                          storage_path=storage_path)
                
                return attachment
            else:
                raise Exception("Failed to create attachment metadata")
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to upload attachment",
                      claim_id=claim_id,
                      filename=filename,
                      error=str(e))
            raise
    
    async def get_signed_url(self, storage_path: str, expires_in: int = 3600) -> Optional[str]:
        """
        Generate signed URL for secure file access
        
        Args:
            storage_path: Path to file in storage
            expires_in: URL expiration time in seconds (default: 1 hour)
            
        Returns:
            Signed URL or None if failed
        """
        try:
            result = self.client.storage.from_(self.storage_bucket).create_signed_url(
                path=storage_path,
                expires_in=expires_in
            )
            
            if result.get('signedURL'):
                return result['signedURL']
            else:
                dozzle_log("warning", "⚠️ [SUPABASE] Failed to generate signed URL",
                          storage_path=storage_path)
                return None
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Error generating signed URL",
                      storage_path=storage_path,
                      error=str(e))
            return None
    
    async def update_attachment(self, attachment_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update attachment metadata"""
        try:
            result = self.client.table('attachments').update(updates).eq('id', attachment_id).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise Exception(f"Attachment not found: {attachment_id}")
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to update attachment",
                      attachment_id=attachment_id,
                      error=str(e))
            raise
    
    async def create_zendesk_ticket_record(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create Zendesk ticket tracking record"""
        try:
            ticket_data.update({
                'id': str(uuid.uuid4()),
                'created_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat(),
                'last_sync_at': datetime.utcnow().isoformat()
            })
            
            result = self.client.table('zendesk_tickets').insert(ticket_data).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise Exception("Failed to create Zendesk ticket record")
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to create Zendesk ticket record",
                      error=str(e))
            raise
    
    async def add_claim_history(self, claim_id: str, event_type: str, description: str, 
                               old_values: Optional[Dict] = None, new_values: Optional[Dict] = None,
                               metadata: Optional[Dict] = None) -> Dict[str, Any]:
        """Add entry to claim history for audit trail"""
        try:
            history_data = {
                'id': str(uuid.uuid4()),
                'claim_id': claim_id,
                'event_type': event_type,
                'event_description': description,
                'old_values': old_values,
                'new_values': new_values,
                'triggered_by': 'system',
                'metadata': metadata,  # Maps to metadata_json in model but metadata in DB
                'created_at': datetime.utcnow().isoformat()
            }
            
            result = self.client.table('claim_history').insert(history_data).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise Exception("Failed to create claim history entry")
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to add claim history",
                      claim_id=claim_id,
                      event_type=event_type,
                      error=str(e))
            raise

    async def get_claim_by_id(self, claim_id: str) -> Optional[Dict[str, Any]]:
        """Get claim by ID"""
        try:
            result = self.client.table('claims').select('*').eq('id', claim_id).execute()
            
            if result.data:
                claim = result.data[0]
                dozzle_log("info", "✅ [SUPABASE] Claim found by ID",
                          claim_id=claim_id,
                          workflow_id=claim.get('workflow_id'),
                          status=claim.get('status'))
                return claim
            else:
                dozzle_log("warning", "⚠️ [SUPABASE] Claim not found by ID",
                          claim_id=claim_id)
                return None
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to get claim by ID",
                      claim_id=claim_id,
                      error=str(e))
            raise

    async def download_file(self, storage_path: str) -> Optional[bytes]:
        """
        Download file from Supabase storage
        
        Args:
            storage_path: Path to file in storage
            
        Returns:
            File content as bytes or None if failed
        """
        try:
            dozzle_log("info", "📥 [SUPABASE] Downloading file from storage",
                      storage_path=storage_path,
                      bucket=self.storage_bucket)
            
            # Download file from Supabase storage
            result = self.client.storage.from_(self.storage_bucket).download(storage_path)
            
            if result:
                dozzle_log("info", "✅ [SUPABASE] File downloaded successfully",
                          storage_path=storage_path,
                          file_size=len(result))
                return result
            else:
                dozzle_log("warning", "⚠️ [SUPABASE] File download returned empty result",
                          storage_path=storage_path)
                return None
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to download file",
                      storage_path=storage_path,
                      error=str(e))
            return None

    async def update_attachment_ocr(self, 
                                   attachment_id: str,
                                   ocr_text: Optional[str],
                                   ocr_confidence: Optional[float],
                                   document_type: Optional[str],
                                   processing_metadata: Optional[Dict[str, Any]],
                                   processed_at: datetime) -> Dict[str, Any]:
        """
        Update attachment with OCR processing results
        
        Args:
            attachment_id: ID of the attachment to update
            ocr_text: Extracted text from OCR
            ocr_confidence: Confidence score (0.0 to 1.0)
            document_type: Type of document identified
            processing_metadata: Additional processing metadata
            processed_at: Timestamp when processing completed
            
        Returns:
            Updated attachment record
        """
        try:
            updates = {
                'ocr_text': ocr_text,
                'ocr_confidence': ocr_confidence,
                'document_type': document_type,
                'processing_metadata': processing_metadata,
                'processed_at': processed_at.isoformat(),
                'upload_status': 'processed' if ocr_text else 'failed'
            }
            
            result = self.client.table('attachments').update(updates).eq('id', attachment_id).execute()
            
            if result.data:
                attachment = result.data[0]
                dozzle_log("info", "✅ [SUPABASE] Attachment OCR results updated",
                          attachment_id=attachment_id,
                          filename=attachment.get('original_filename'),
                          ocr_confidence=ocr_confidence,
                          text_length=len(ocr_text) if ocr_text else 0,
                          document_type=document_type)
                return attachment
            else:
                raise Exception(f"Attachment not found for OCR update: {attachment_id}")
                
        except Exception as e:
            dozzle_log("error", "❌ [SUPABASE] Failed to update attachment OCR results",
                      attachment_id=attachment_id,
                      error=str(e))
            raise
