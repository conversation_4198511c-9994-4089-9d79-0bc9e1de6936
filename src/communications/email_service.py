"""
Professional Email Service for Zurich Claims Processing

Handles professional acknowledgment emails and customer communications
with enterprise-grade templates and business-appropriate formatting.
"""

# Standard library imports
import email
import email.message
import email.utils
from email.header import decode_header
from email.utils import parseaddr, formataddr, make_msgid, formatdate
from email.header import Header
import smtplib
import ssl
from datetime import datetime
from typing import Dict, Any, Optional, List
import re
import structlog
import uuid
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

# Remove unused enhanced parsing dependency that causes warnings
# We use standard Python email library which works perfectly for our needs

from ..config.settings import Settings
from ..utils.dozzle_logger import dozzle_log
from .email_threading import EmailThreadingManager

logger = structlog.get_logger(__name__)


class EmailThreadInfo:
    """Container for email threading information"""
    def __init__(
        self,
        message_id: Optional[str] = None,
        in_reply_to: Optional[str] = None,
        references: Optional[List[str]] = None,
        original_subject: Optional[str] = None,
        original_sender: Optional[str] = None,
        original_date: Optional[datetime] = None,
        original_body_preview: Optional[str] = None
    ):
        self.message_id = message_id
        self.in_reply_to = in_reply_to
        self.references = references or []
        self.original_subject = original_subject
        self.original_sender = original_sender
        self.original_date = original_date
        self.original_body_preview = original_body_preview


class EmailParser:
    """Utility class for parsing email messages and extracting threading information"""
    
    @staticmethod
    def parse_raw_email(raw_email: str) -> email.message.EmailMessage:
        """Parse raw email string into EmailMessage object"""
        return email.message_from_string(raw_email)
    
    @staticmethod
    def parse_email_file(email_path: str) -> email.message.EmailMessage:
        """Parse email from file"""
        with open(email_path, 'r', encoding='utf-8') as f:
            return email.message_from_file(f)
    
    @staticmethod
    def extract_thread_info(msg: email.message.EmailMessage) -> EmailThreadInfo:
        """Extract threading information from email message"""
        
        # Extract Message-ID
        message_id = msg.get('Message-ID', '').strip('<>')
        
        # Extract In-Reply-To
        in_reply_to = msg.get('In-Reply-To', '').strip('<>')
        
        # Extract References
        references_header = msg.get('References', '')
        references = []
        if references_header:
            # References can be space or newline separated
            references = [ref.strip('<>') for ref in re.findall(r'<[^>]+>', references_header)]
        
        # Extract subject
        subject = EmailParser._decode_header(msg.get('Subject', ''))
        
        # Extract sender
        from_header = msg.get('From', '')
        sender_name, sender_email = parseaddr(from_header)
        original_sender = sender_email
        
        # Extract date
        date_header = msg.get('Date', '')
        original_date = None
        if date_header:
            try:
                original_date = email.utils.parsedate_to_datetime(date_header)
            except:
                pass
        
        # Extract body preview (first 200 chars)
        body_preview = EmailParser._extract_body_preview(msg)
        
        return EmailThreadInfo(
            message_id=message_id,
            in_reply_to=in_reply_to,
            references=references,
            original_subject=subject,
            original_sender=original_sender,
            original_date=original_date,
            original_body_preview=body_preview
        )
    
    @staticmethod
    def _decode_header(header_value: str) -> str:
        """Decode email header that might be encoded"""
        if not header_value:
            return ""
        
        decoded_parts = decode_header(header_value)
        decoded_string = ""
        
        for part, encoding in decoded_parts:
            if isinstance(part, bytes):
                decoded_string += part.decode(encoding or 'utf-8')
            else:
                decoded_string += part
        
        return decoded_string.strip()
    
    @staticmethod
    def _extract_body_preview(msg: email.message.EmailMessage, max_length: int = 200) -> str:
        """Extract a preview of the email body with enhanced reply parsing"""
        body = ""
        
        # Try to get plain text first
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    body = part.get_payload(decode=True)
                    if isinstance(body, bytes):
                        body = body.decode('utf-8', errors='ignore')
                    break
        else:
            if msg.get_content_type() == "text/plain":
                body = msg.get_payload(decode=True)
                if isinstance(body, bytes):
                    body = body.decode('utf-8', errors='ignore')
        
        if not body:
            # Fall back to HTML if no plain text
            if msg.is_multipart():
                for part in msg.walk():
                    if part.get_content_type() == "text/html":
                        body = part.get_payload(decode=True)
                        if isinstance(body, bytes):
                            body = body.decode('utf-8', errors='ignore')
                        # Strip HTML tags for preview
                        body = re.sub('<[^<]+?>', '', body)
                        break
        
        # Clean up the body
        if body:
            # Remove extra whitespace and newlines
            body = re.sub(r'\s+', ' ', body).strip()
            # Truncate to max length
            if len(body) > max_length:
                body = body[:max_length] + "..."
        
        return body or "[No preview available]"


class ProfessionalEmailService:
    """
    Professional Email Service with Enhanced Threading Support
    
    Integrates with EmailThreadingManager for:
    - Professional Message-ID generation with claim references
    - Multi-language reply content extraction
    - RFC-compliant threading headers
    - Conversation tracking and analytics
    """
    
    def __init__(self, settings: Settings):
        """Initialize email service with enhanced settings"""
        self.from_email = settings.claims_email
        self.password = settings.claims_email_password
        self.smtp_server = settings.smtp_server
        self.smtp_port = settings.smtp_port
        
        # Extract domain from email for threading
        email_domain = self.from_email.split('@')[1] if '@' in self.from_email else 'zurich.com'
        
        # Initialize enhanced threading manager with correct domain
        self.threading_manager = EmailThreadingManager(domain=email_domain)
        
        # Configure tracking URL
        self.tracking_base_url = settings.tracking_base_url
        
        dozzle_log("info", "📧 [EMAIL_SERVICE] Professional email service initialized",
                  smtp_server=self.smtp_server,
                  smtp_port=self.smtp_port,
                  from_email=self.from_email,
                  threading_domain=email_domain,
                  tracking_url=self.tracking_base_url)
    
    async def send_claim_acknowledgment(
        self,
        claim_id: str,
        customer_email: str,
        customer_name: str,
        claim_type: str,
        incident_date: Optional[str] = None,
        tracking_url: Optional[str] = None,
        thread_info: Optional[EmailThreadInfo] = None,
        zendesk_ticket_id: Optional[str] = None
    ) -> bool:
        """
        Send professional claim acknowledgment with enhanced threading
        
        Args:
            claim_id: Claim reference number
            customer_email: Customer's email address  
            customer_name: Customer's display name
            claim_type: Type of claim being processed
            incident_date: Date of incident (optional)
            tracking_url: URL for claim tracking (optional)
            thread_info: Threading information for replies (optional)
            zendesk_ticket_id: Zendesk ticket ID for comment tracking (optional)
            
        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            # Generate professional Message-ID with claim reference
            message_id = self.threading_manager.generate_message_id(claim_id=claim_id)
            
            # Create proper threading headers
            thread_headers = self.threading_manager.create_thread_headers(
                message_id=message_id,
                in_reply_to=thread_info.message_id if thread_info else None,
                references=thread_info.references if thread_info else None
            )
            
            # Generate tracking URL if not provided
            if not tracking_url:
                tracking_url = self.generate_tracking_url(claim_id)
            
            # Generate email content
            subject = self._generate_acknowledgment_subject(claim_id, claim_type, thread_info)
            html_body = self._generate_acknowledgment_html(
                claim_id, customer_name, claim_type, incident_date, tracking_url, thread_info
            )
            text_body = self._generate_acknowledgment_text(
                claim_id, customer_name, claim_type, incident_date, tracking_url, thread_info
            )
            
            # Send email with professional threading
            success = await self._send_email_with_threading(
                to_email=customer_email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
                thread_headers=thread_headers,
                claim_id=claim_id
            )
            
            # Add Zendesk comment if ticket ID provided
            if success and zendesk_ticket_id:
                await self._add_zendesk_acknowledgment_comment(
                    zendesk_ticket_id, claim_id, customer_email, message_id
                )
            
            return success
            
        except Exception as e:
            dozzle_log("error", "❌ [EMAIL_SERVICE] Failed to send acknowledgment",
                      claim_id=claim_id,
                      customer_email=customer_email,
                      error=str(e))
            return False
    
    def _generate_acknowledgment_subject(self, claim_id: str, claim_type: str, thread_info: Optional[EmailThreadInfo] = None) -> str:
        """Generate professional email subject line with enhanced threading support"""
        
        # Always include claim ID in the subject for consistency
        base_subject = f"Claim Confirmation - Reference #{claim_id}"
        
        if thread_info and thread_info.original_subject:
            # Remove any existing "Re:" prefixes to avoid "Re: Re: Re:" chains
            clean_subject = re.sub(r'^(Re:\s*)+', '', thread_info.original_subject, flags=re.IGNORECASE).strip()
            
            # Also remove existing claim references to avoid duplication
            clean_subject = re.sub(r'\[.*?\]', '', clean_subject).strip()
            
            # Use your requested format: [Zurich Insurance] RE: [claim_id] followed by subject
            threaded_subject = f"[Zurich Insurance] RE: [{claim_id}] {clean_subject}"
            
            dozzle_log("info", "🧵 [EMAIL_THREADING] Generated threaded subject with RE format",
                      claim_id=claim_id,
                      original_subject=thread_info.original_subject,
                      clean_subject=clean_subject,
                      final_subject=threaded_subject,
                      threading_applied=True)
            
            return threaded_subject
        else:
            # First email in thread or no threading info available
            new_subject = f"[Zurich Insurance] {base_subject}"
            
            dozzle_log("info", "📧 [EMAIL_SUBJECT] Generated new subject (no threading)",
                      claim_id=claim_id,
                      final_subject=new_subject,
                      threading_applied=False,
                      reason="no_thread_info" if not thread_info else "no_original_subject")
            
            return new_subject
    
    def _generate_acknowledgment_html(
        self,
        claim_id: str,
        customer_name: str,
        claim_type: str,
        incident_date: Optional[str] = None,
        tracking_url: Optional[str] = None,
        thread_info: Optional[EmailThreadInfo] = None
    ) -> str:
        """Generate professional HTML email body"""
        
        # Format incident date if provided
        incident_date_str = ""
        if incident_date:
            try:
                # Parse and format date professionally
                if "T" in incident_date:
                    date_obj = datetime.fromisoformat(incident_date.replace('Z', '+00:00'))
                    incident_date_str = date_obj.strftime("%B %d, %Y")
                else:
                    incident_date_str = incident_date
            except:
                incident_date_str = incident_date
        
        # Generate tracking link section
        tracking_section = ""
        if tracking_url:
            tracking_section = f"""
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #005AAF; margin: 0 0 10px 0;">Track Your Claim Status</h3>
                <p style="margin: 0 0 15px 0;">You can track the progress of your claim at any time using the link below:</p>
                <div style="text-align: center;">
                    <a href="{tracking_url}" style="background-color: #005AAF; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Track Your Claim</a>
                </div>
                <p style="margin: 15px 0 0 0; font-size: 14px; color: #666;">Reference Number: {claim_id}</p>
            </div>
            """
        
        # Generate incident date section
        incident_section = ""
        if incident_date_str:
            incident_section = f"""
            <tr>
                <td style="padding: 8px 15px; border-bottom: 1px solid #eee; font-weight: bold;">Incident Date:</td>
                <td style="padding: 8px 15px; border-bottom: 1px solid #eee;">{incident_date_str}</td>
            </tr>
            """
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Claim Confirmation - Zurich Insurance</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; background-color: #f4f4f4; padding: 20px;">
            <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <!-- Header -->
                <div style="text-align: center; border-bottom: 3px solid #005AAF; padding-bottom: 20px; margin-bottom: 30px;">
                    <h1 style="color: #005AAF; margin: 0; font-size: 28px;">Zurich Insurance</h1>
                    <p style="color: #666; margin: 5px 0 0 0; font-size: 16px;">Claims Processing Department</p>
                </div>
                
                <!-- Main Content -->
                <div>
                    <h2 style="color: #005AAF; margin: 0 0 20px 0;">Claim Received - We're Here to Help</h2>
                    
                    <p>Dear {customer_name},</p>
                    
                    <p>Thank you for submitting your claim to Zurich Insurance. We have successfully received your request and wanted to confirm the details with you immediately.</p>
                    
                    <!-- Claim Details Table -->
                    <div style="margin: 25px 0;">
                        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd; border-radius: 5px; overflow: hidden;">
                            <tr>
                                <td style="padding: 8px 15px; border-bottom: 1px solid #eee; font-weight: bold; background-color: #f8f9fa;">Claim Reference:</td>
                                <td style="padding: 8px 15px; border-bottom: 1px solid #eee; background-color: #f8f9fa;">{claim_id}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 15px; border-bottom: 1px solid #eee; font-weight: bold;">Claim Type:</td>
                                <td style="padding: 8px 15px; border-bottom: 1px solid #eee;">{claim_type}</td>
                            </tr>
                            {incident_section}
                            <tr>
                                <td style="padding: 8px 15px; font-weight: bold;">Received Date:</td>
                                <td style="padding: 8px 15px;">{datetime.now().strftime("%B %d, %Y at %I:%M %p")}</td>
                            </tr>
                        </table>
                    </div>
                    
                    {tracking_section}
                    
                    <!-- Next Steps -->
                    <div style="margin: 30px 0;">
                        <h3 style="color: #005AAF; margin: 0 0 15px 0;">What Happens Next</h3>
                        <ol style="padding-left: 20px;">
                            <li style="margin-bottom: 10px;"><strong>Document Validation:</strong> We will verify all submitted documents for completeness and accuracy.</li>
                            <li style="margin-bottom: 10px;"><strong>Claim Assessment:</strong> Our experienced claims team will conduct a thorough evaluation of your claim details.</li>
                            <li style="margin-bottom: 10px;"><strong>Executive Review:</strong> A senior claims executive will be assigned for final approval and decision.</li>
                        </ol>
                    </div>
                    
                    <!-- Contact Information -->
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
                        <h3 style="color: #005AAF; margin: 0 0 10px 0;">Need Assistance?</h3>
                        <p style="margin: 0 0 10px 0;">We will be reaching out to you in this email thread if we need any additional information or documentation.</p>
                        <p style="margin: 0 0 10px 0;"><strong>Email:</strong> {self.from_email}</p>
                        <p style="margin: 0;">Our claims processing team is available Monday through Friday, 8:00 AM to 6:00 PM.</p>
                    </div>
                    
                    <!-- Closing -->
                    <div style="margin: 30px 0 20px 0;">
                        <p>We appreciate your trust in Zurich Insurance and are committed to processing your claim efficiently and fairly. You can expect regular updates on the progress of your claim.</p>
                        
                        <p style="margin: 20px 0 0 0;">
                            Sincerely,<br>
                            <strong>Zurich Claims Processing Team</strong><br>
                            Zurich Insurance Company
                        </p>
                    </div>
                </div>
                
                {self._generate_original_message_context_html(thread_info)}
                
                <!-- Footer -->
                <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px; text-align: center; color: #666; font-size: 14px;">
                    <p style="margin: 0;">This is an automated confirmation. Please do not reply to this email.</p>
                    <p style="margin: 5px 0 0 0;">© {datetime.now().year} Zurich Insurance Company. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_template
    
    def _generate_original_message_context_html(self, thread_info: Optional[EmailThreadInfo]) -> str:
        """Generate HTML section for original message context when replying in thread"""
        if not thread_info or not thread_info.original_body_preview:
            return ""
        
        original_date_str = ""
        if thread_info.original_date:
            original_date_str = thread_info.original_date.strftime("%B %d, %Y at %I:%M %p")
        
        context_html = f"""
                <!-- Original Message Context -->
                <div style="margin-top: 40px; padding-top: 20px; border-top: 2px solid #e0e0e0;">
                    <h4 style="color: #005AAF; margin: 0 0 15px 0;">Original Message</h4>
                    <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #005AAF; margin: 15px 0;">
                        <p style="margin: 0 0 10px 0; font-size: 14px; color: #666;">
                            <strong>From:</strong> {thread_info.original_sender or 'Unknown'}<br>
                            <strong>Date:</strong> {original_date_str}<br>
                            <strong>Subject:</strong> {thread_info.original_subject or 'No subject'}
                        </p>
                        <div style="border-top: 1px solid #ddd; padding-top: 10px; margin-top: 10px;">
                            <p style="margin: 0; font-style: italic; color: #555; font-size: 14px;">
                                "{thread_info.original_body_preview}"
                            </p>
                        </div>
                    </div>
                </div>
        """
        
        return context_html
    
    def _generate_acknowledgment_text(
        self,
        claim_id: str,
        customer_name: str,
        claim_type: str,
        incident_date: Optional[str] = None,
        tracking_url: Optional[str] = None,
        thread_info: Optional[EmailThreadInfo] = None
    ) -> str:
        """Generate plain text email body as fallback"""
        
        incident_text = ""
        if incident_date:
            try:
                if "T" in incident_date:
                    date_obj = datetime.fromisoformat(incident_date.replace('Z', '+00:00'))
                    incident_text = f"\nIncident Date: {date_obj.strftime('%B %d, %Y')}"
                else:
                    incident_text = f"\nIncident Date: {incident_date}"
            except:
                incident_text = f"\nIncident Date: {incident_date}"
        
        tracking_text = ""
        if tracking_url:
            tracking_text = f"""

TRACK YOUR CLAIM STATUS:
You can track the progress of your claim at any time: {tracking_url}
Reference Number: {claim_id}
"""
        
        text_template = f"""
ZURICH INSURANCE - CLAIM CONFIRMATION

Dear {customer_name},

Thank you for submitting your claim to Zurich Insurance. We have successfully received your request and wanted to confirm the details with you immediately.

CLAIM DETAILS:
Claim Reference: {claim_id}
Claim Type: {claim_type}{incident_text}
Received Date: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}
{tracking_text}

WHAT HAPPENS NEXT:

1. Document Validation: We will verify all submitted documents for completeness and accuracy.

2. Claim Assessment: Our experienced claims team will conduct a thorough evaluation of your claim details.

3. Executive Review: A senior claims executive will be assigned for final approval and decision.

NEED ASSISTANCE?
We will be reaching out to you in this email thread if we need any additional information or documentation.

Email: {self.from_email}
Our claims processing team is available Monday through Friday, 8:00 AM to 6:00 PM.

We appreciate your trust in Zurich Insurance and are committed to processing your claim efficiently and fairly. You can expect regular updates on the progress of your claim.

Sincerely,
Zurich Claims Processing Team
Zurich Insurance Company

---
This is an automated confirmation. Please do not reply to this email.
© {datetime.now().year} Zurich Insurance Company. All rights reserved.

{self._generate_original_message_context_text(thread_info)}
        """
        
        return text_template.strip()
    
    def _generate_original_message_context_text(self, thread_info: Optional[EmailThreadInfo]) -> str:
        """Generate text section for original message context when replying in thread"""
        if not thread_info or not thread_info.original_body_preview:
            return ""
        
        original_date_str = ""
        if thread_info.original_date:
            original_date_str = thread_info.original_date.strftime("%B %d, %Y at %I:%M %p")
        
        context_text = f"""

--- ORIGINAL MESSAGE ---
From: {thread_info.original_sender or 'Unknown'}
Date: {original_date_str}
Subject: {thread_info.original_subject or 'No subject'}

"{thread_info.original_body_preview}"
--- END ORIGINAL MESSAGE ---
"""
        
        return context_text
    
    async def _send_email_with_threading(
        self,
        to_email: str,
        subject: str,
        html_body: str,
        text_body: str,
        thread_headers: Dict[str, str],
        claim_id: Optional[str] = None,
        max_retries: int = 3
    ) -> bool:
        """
        Send email with professional threading headers and Gmail optimization
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            html_body: HTML email body  
            text_body: Plain text email body
            thread_headers: Professional threading headers
            claim_id: Claim ID for logging
            max_retries: Maximum retry attempts
            
        Returns:
            True if email sent successfully, False otherwise
        """
        for attempt in range(max_retries):
            try:
                # Create message
                msg = MIMEMultipart('alternative')
                msg['From'] = formataddr(("Zurich Claims Team", self.from_email))
                msg['To'] = to_email
                msg['Subject'] = Header(subject, 'utf-8')
                
                # Add professional threading headers
                for header_name, header_value in thread_headers.items():
                    msg[header_name] = header_value
                
                # Add Gmail-specific threading optimization headers
                if claim_id:
                    # Add List-ID for better organization in Gmail
                    msg['List-ID'] = f"zurich-claims-{claim_id} <claims.zurich.com>"
                    
                    # Add X-Entity-ID for consistent conversation grouping
                    msg['X-Entity-ID'] = f"claim-{claim_id}"
                
                # Add threading debug headers for monitoring
                msg['X-Threading-Info'] = f"claim-{claim_id or 'unknown'}"
                
                # Ensure consistent Date header for threading
                msg['Date'] = formatdate(localtime=True)
                
                # Add both plain text and HTML versions
                text_part = MIMEText(text_body, 'plain')
                html_part = MIMEText(html_body, 'html')
                
                msg.attach(text_part)
                msg.attach(html_part)
                
                # Create SMTP connection
                context = ssl.create_default_context()
                with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                    server.starttls(context=context)
                    server.login(self.from_email, self.password)
                    server.send_message(msg)
                
                dozzle_log("info", "✅ [EMAIL_SERVICE] Professional email sent with enhanced threading",
                          to_email=to_email,
                          subject=subject,
                          claim_id=claim_id,
                          message_id=thread_headers.get('Message-ID'),
                          in_reply_to=thread_headers.get('In-Reply-To'),
                          has_references=bool(thread_headers.get('References')),
                          attempt=attempt + 1,
                          threading_enabled=True,
                          gmail_optimized=True)
                return True
                
            except Exception as e:
                dozzle_log("warning", f"⚠️ [EMAIL_SERVICE] Email send attempt {attempt + 1} failed",
                          to_email=to_email,
                          claim_id=claim_id,
                          error=str(e),
                          attempt=attempt + 1,
                          max_retries=max_retries)
                
                if attempt == max_retries - 1:
                    dozzle_log("error", "❌ [EMAIL_SERVICE] All email send attempts failed",
                              to_email=to_email,
                              claim_id=claim_id,
                              error=str(e),
                              total_attempts=max_retries)
                    return False
                
                # Brief delay before retry
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        
        return False
    
    async def _add_zendesk_acknowledgment_comment(
        self,
        zendesk_ticket_id: str,
        claim_id: str,
        customer_email: str,
        message_id: str
    ) -> None:
        """
        Add comment to Zendesk ticket after sending acknowledgment email
        
        Args:
            zendesk_ticket_id: Zendesk ticket ID
            claim_id: Claim reference number
            customer_email: Customer's email address
            message_id: Email Message-ID for tracking
        """
        try:
            # Import here to avoid circular dependencies
            from ..zendesk_integration.zendesk_client import ZendeskClient
            from ..database.supabase_client import SupabaseClient
            
            # Initialize clients
            settings = Settings()
            supabase = SupabaseClient(settings)
            zendesk = ZendeskClient(settings, supabase)
            
            # Prepare comment content
            comment_body = f"""**Acknowledgment Email Sent**

**Status**: Claim acknowledgment email successfully sent to customer
**Claim Reference**: {claim_id}
**Recipient**: {customer_email}
**Message-ID**: {message_id}
**Sent**: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}

**Email Threading**: Professional Message-ID with claim reference generated for proper email threading continuity.

**Next Actions**:
- Customer will receive tracking URL for self-service status updates
- All future emails will maintain proper threading headers
- Monitor for customer responses in this email thread

---
*This comment was automatically generated after successful email delivery.*"""
            
            # Add comment to ticket
            await zendesk.add_comment_to_ticket(
                ticket_id=zendesk_ticket_id,
                comment=comment_body,
                public=False  # Internal comment
            )
            
            dozzle_log("info", "📝 [EMAIL_SERVICE] Zendesk acknowledgment comment added",
                      ticket_id=zendesk_ticket_id,
                      claim_id=claim_id,
                      message_id=message_id)
                      
        except Exception as e:
            dozzle_log("warning", "⚠️ [EMAIL_SERVICE] Failed to add Zendesk comment",
                      ticket_id=zendesk_ticket_id,
                      claim_id=claim_id,
                      error=str(e))
            # Don't fail the email sending if comment fails
    
    def generate_tracking_url(self, claim_id: str, base_url: Optional[str] = None) -> str:
        """
        Generate tracking URL for claim
        
        Args:
            claim_id: Claim identifier
            base_url: Base URL for tracking (optional, overrides configured URL)
            
        Returns:
            Complete tracking URL
        """
        # Use provided base_url, then configured base_url, then fallback
        effective_base_url = base_url or self.tracking_base_url
        
        # Ensure the base URL doesn't end with a slash
        effective_base_url = effective_base_url.rstrip('/')
        
        # For local development, use the tracking route format
        if 'localhost' in effective_base_url or '127.0.0.1' in effective_base_url:
            return f"{effective_base_url}/tracking?claim={claim_id}"
        
        # For production, use the same format as the analyzed website
        return f"{effective_base_url}/tracking?claim={claim_id}" 