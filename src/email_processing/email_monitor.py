"""
Email Monitor Service
Monitors the claims inbox for new emails and triggers the workflow
"""

import asyncio
import email
import email.message
import imaplib
import logging
from datetime import datetime
from email.header import decode_header
from typing import Dict, List, Any, Callable
import os

from src.utils.dozzle_logger import dozzle_log

logger = logging.getLogger(__name__)

class EmailMonitor:
    """Monitors email inbox for new claims"""
    
    def __init__(self, settings, workflow_coordinator: Any = None):
        self.settings = settings
        self.workflow_coordinator = workflow_coordinator
        
        # 🚨 SECURITY: Use ONLY environment variables - NO hardcoded fallbacks
        self.email_address = settings.claims_email
        self.password = settings.claims_email_password
        self.imap_server = settings.imap_server
        self.imap_port = settings.imap_port
        
        # Validate required credentials
        if not self.email_address:
            raise ValueError("EMAIL environment variable is required")
        if not self.password:
            raise ValueError("CLAIMS_EMAIL_PASSWORD environment variable is required")
        self.is_monitoring = False
        self.processed_emails = set()
        
        logger.info(f"Email monitor initialized for {self.email_address}")
    
    async def start_monitoring(self, callback: Callable[[Dict[str, Any]], None] = None):
        """Start monitoring the email inbox"""
        try:
            self.is_monitoring = True
            self.callback = callback or self._default_callback
            
            logger.info("Starting email monitoring...")
            
            # Start monitoring loop
            asyncio.create_task(self._monitor_loop())
            
            logger.info("Email monitoring started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start email monitoring: {e}")
            raise
    
    async def stop_monitoring(self):
        """Stop monitoring the email inbox"""
        self.is_monitoring = False
        logger.info("Email monitoring stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                await self._check_new_emails()
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in email monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _check_new_emails(self):
        """Check for new emails in the inbox"""
        try:
            # Connect to IMAP server
            mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            mail.login(self.email_address, self.password)
            mail.select('INBOX')
            
            # Search for unread emails
            status, messages = mail.search(None, 'UNSEEN')
            
            if status == 'OK' and messages[0]:
                email_ids = messages[0].split()
                
                for email_id in email_ids:
                    await self._process_email(mail, email_id)
            
            mail.close()
            mail.logout()
            
        except Exception as e:
            logger.error(f"Error checking emails: {e}")
    
    async def _process_email(self, mail: imaplib.IMAP4_SSL, email_id: bytes):
        """Process a single email"""
        try:
            # Fetch email data
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            
            if status == 'OK':
                email_body = msg_data[0][1]
                email_message = email.message_from_bytes(email_body)
                
                # Extract email information
                email_data = await self._extract_email_data(email_message)
                
                # Check if we've already processed this email
                email_hash = self._generate_email_hash(email_data)
                if email_hash in self.processed_emails:
                    return
                
                # Mark as processed
                self.processed_emails.add(email_hash)
                
                # Call the callback to process the email
                await self.callback(email_data)
                
                # Mark email as read
                mail.store(email_id, '+FLAGS', '\\Seen')
                
                logger.info(f"Processed email: {email_data.get('subject', 'No subject')}")
            
        except Exception as e:
            logger.error(f"Error processing email {email_id}: {e}")
    
    async def _extract_email_data(self, email_message: email.message.Message) -> Dict[str, Any]:
        """Extract relevant data from email message"""
        try:
            # Extract headers
            subject = self._decode_header(email_message.get('Subject', ''))
            from_email = self._decode_header(email_message.get('From', ''))
            to_email = self._decode_header(email_message.get('To', ''))
            date = email_message.get('Date', '')
            message_id = email_message.get('Message-ID', '')
            
            # Extract sender name and email
            from_name, from_email_clean = self._parse_email_address(from_email)
            
            # Extract body
            body = await self._extract_body(email_message)
            
            # Extract attachments
            attachments = await self._extract_attachments(email_message)
            
            # Convert to workflow coordinator format (clean IMAP format)
            email_data = {
                # Clean direct IMAP format for workflow processing
                'from_address': from_email_clean,
                'to_address': to_email,
                'subject': subject,
                'body': body,  # This will be CLEAN, not corrupted!
                'message_id': message_id,
                'raw_email': email_message.as_string(),  # Full raw email
                'previous_thread': [],  # Can be enhanced later
                
                # Additional fields for enhanced processing
                'from_name': from_name,
                'sender_email': from_email_clean,
                'sender_name': from_name,
                'received_at': datetime.now().isoformat() + 'Z',
                'date': date,
                
                # Attachment information
                'attachments': attachments,
                'has_attachments': len(attachments) > 0,
                'attachments_mentioned': self._check_attachments_mentioned(body),
                'attachment_count': len(attachments),
                
                # Processing metadata
                'processed_at': datetime.now().isoformat() + 'Z',
                'source': 'direct_imap',
                'corruption_detected': False,  # IMAP emails won't be corrupted!
                'webhook_metadata': {
                    'event_type': 'direct_email.received',
                    'is_test': False,
                    'source': 'imap_monitor',
                    'body_parsing': {
                        'source': 'direct_imap',
                        'original_length': len(body),
                        'final_length': len(body),
                        'improvement_ratio': 1.0  # No corruption, no improvement needed
                    }
                }
            }
            
            return email_data
            
        except Exception as e:
            logger.error(f"Error extracting email data: {e}")
            return {}
    
    def _decode_header(self, header: str) -> str:
        """Decode email header"""
        try:
            if header:
                decoded_parts = decode_header(header)
                decoded_string = ""
                for part, encoding in decoded_parts:
                    if isinstance(part, bytes):
                        if encoding:
                            decoded_string += part.decode(encoding)
                        else:
                            decoded_string += part.decode('utf-8', errors='ignore')
                    else:
                        decoded_string += part
                return decoded_string
            return ""
        except Exception as e:
            logger.error(f"Error decoding header: {e}")
            return str(header) if header else ""
    
    def _parse_email_address(self, email_string: str) -> tuple:
        """Parse email address to extract name and email"""
        try:
            if '<' in email_string and '>' in email_string:
                # <AUTHOR> <EMAIL>"
                name_part = email_string.split('<')[0].strip().strip('"')
                email_part = email_string.split('<')[1].split('>')[0].strip()
                return name_part, email_part
            else:
                # Format: "<EMAIL>"
                return "", email_string.strip()
        except Exception as e:
            logger.error(f"Error parsing email address: {e}")
            return "", email_string
    
    async def _extract_body(self, email_message: email.message.Message) -> str:
        """Extract email body"""
        try:
            body = ""
            
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get('Content-Disposition', ''))
                    
                    # Skip attachments
                    if 'attachment' in content_disposition:
                        continue
                    
                    # Get text content
                    if content_type == 'text/plain':
                        try:
                            payload = part.get_payload(decode=True)
                            charset = part.get_content_charset() or 'utf-8'
                            body += payload.decode(charset, errors='ignore')
                        except Exception as e:
                            logger.error(f"Error decoding text part: {e}")
                    
                    elif content_type == 'text/html':
                        # Convert HTML to plain text (basic conversion)
                        try:
                            payload = part.get_payload(decode=True)
                            charset = part.get_content_charset() or 'utf-8'
                            html_content = payload.decode(charset, errors='ignore')
                            # Basic HTML to text conversion
                            import re
                            text_content = re.sub(r'<[^>]+>', '', html_content)
                            text_content = re.sub(r'\s+', ' ', text_content).strip()
                            body += text_content
                        except Exception as e:
                            logger.error(f"Error decoding HTML part: {e}")
            else:
                # Not multipart
                payload = email_message.get_payload(decode=True)
                charset = email_message.get_content_charset() or 'utf-8'
                body = payload.decode(charset, errors='ignore')
            
            return body.strip()
            
        except Exception as e:
            logger.error(f"Error extracting email body: {e}")
            return ""
    
    async def _extract_attachments(self, email_message: email.message.Message) -> List[Dict[str, Any]]:
        """Extract attachments from email message and return data directly as bytes"""
        attachments = []
        part_number = 0
        
        try:
            logger.info(f"📎 [ATTACHMENT_SCAN] Scanning email for attachments")
            
            for part in email_message.walk():
                part_number += 1
                content_disposition = part.get('Content-Disposition', '')
                content_type = part.get_content_type()
                
                # Extract filename
                filename = self._extract_filename(part, content_type, part_number)
                
                logger.info(f"🔍 [PART_{part_number}] content_type={content_type}, disposition='{content_disposition}', filename='{filename}'")
                
                # Check if this is an attachment
                if self._is_attachment_part(content_disposition, content_type, filename):
                    logger.info(f"📎 [ATTACHMENT_FOUND] Processing attachment: {filename}")

                    # Generate claim ID if not provided (for organized temp storage)
                    if not hasattr(self, '_current_claim_id'):
                        import uuid
                        self._current_claim_id = f"claim_{uuid.uuid4().hex[:8]}"
                        logger.info(f"🆔 [CLAIM_ID] Generated: {self._current_claim_id}")

                    # Extract to organized temp file: tmp/claimid/files/filename
                    temp_file_path = self._extract_payload_to_organized_temp_file(part, filename, self._current_claim_id)

                    if temp_file_path:
                        try:
                            # Read clean binary data from organized temp file
                            with open(temp_file_path, 'rb') as temp_file:
                                payload = temp_file.read()

                            if payload and len(payload) > 0:
                                # 🚀 ORGANIZED STORAGE: Store file path for later upload to Supabase
                                attachment_data = {
                                    'filename': filename,
                                    'content_type': content_type,
                                    'size': len(payload),
                                    'content': payload,  # Clean bytes from organized temp file
                                    'temp_file_path': temp_file_path,  # Keep path for OCR processing
                                    'claim_id': self._current_claim_id,
                                    'source': 'email_attachment_organized'
                                }

                                attachments.append(attachment_data)
                                logger.info(f"✅ [ATTACHMENT_ORGANIZED] {filename}: {len(payload)} bytes stored in {temp_file_path}")
                            else:
                                logger.warning(f"⚠️ [ATTACHMENT_EMPTY] Organized temp file empty for {filename}")
                                # Clean up empty file
                                if os.path.exists(temp_file_path):
                                    os.unlink(temp_file_path)

                        except Exception as read_error:
                            logger.error(f"❌ [ATTACHMENT_READ_ERROR] Failed to read {temp_file_path}: {read_error}")
                            # Clean up on read error
                            if os.path.exists(temp_file_path):
                                os.unlink(temp_file_path)
                    else:
                        logger.warning(f"⚠️ [ATTACHMENT_SKIP] Failed to extract {filename} to organized temp file")
                
            logger.info(f"📎 [ATTACHMENT_SCAN_COMPLETE] Found {len(attachments)} attachments")
            return attachments
            
        except Exception as e:
            logger.error(f"❌ [ATTACHMENT_SCAN_ERROR] Error extracting attachments: {e}")
            return []

    def _is_attachment_part(self, content_disposition: str, content_type: str, filename: str) -> bool:
        """Enhanced attachment detection with more restrictive criteria to avoid fake attachments"""
        try:
            # Skip if no filename and content type is text or multipart (avoid fake attachments)
            if not filename and (content_type.startswith('text/') or content_type.startswith('multipart/')):
                return False
            
            # Skip common email content types that aren't attachments
            skip_types = [
                'text/plain', 'text/html', 'text/alternative',
                'multipart/mixed', 'multipart/alternative', 'multipart/related'
            ]
            if content_type in skip_types:
                logger.info(f"🚫 [SKIP_EMAIL_PART] Skipping email content part: {content_type}")
                return False
            
            # Method 1: Traditional attachment disposition (most reliable)
            if 'attachment' in content_disposition.lower():
                logger.info(f"🎯 [DETECT_METHOD_1] Found via 'attachment' in disposition")
                return True

            # Method 2: Inline disposition with filename AND not a text type (for embedded files)
            if ('inline' in content_disposition.lower() and filename and 
                not content_type.startswith('text/')):
                logger.info(f"🎯 [DETECT_METHOD_2] Found via 'inline' with filename (non-text)")
                return True

            # Method 3: Common document/media file types with filename (more specific)
            if filename:
                document_types = [
                    'application/pdf', 'application/msword', 'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument', 'application/zip',
                    'application/x-zip-compressed', 'application/octet-stream',
                    'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff',
                    'image/jpg', 'application/rtf', 'application/x-pdf'
                ]
                
                for doc_type in document_types:
                    if content_type.startswith(doc_type):
                        logger.info(f"🎯 [DETECT_METHOD_3] Found document type with filename: {content_type}")
                        return True

            # Method 4: Form-data disposition with filename (common in some email clients)
            if ('form-data' in content_disposition.lower() and 
                'filename' in content_disposition.lower() and 
                not content_type.startswith('text/')):
                logger.info(f"🎯 [DETECT_METHOD_4] Found via 'form-data' with filename (non-text)")
                return True

            # If we get here, it's likely not a real attachment
            logger.info(f"🚫 [NOT_ATTACHMENT] Not detected as attachment: type={content_type}, filename={filename}")
            return False

        except Exception as e:
            logger.error(f"❌ [DETECT_ERROR] Error in attachment detection: {e}")
            return False

    def _extract_filename(self, part, content_type: str, part_number: int) -> str:
        """Extract filename with multiple fallback methods"""
        try:
            # Method 1: Standard get_filename()
            filename = part.get_filename()
            if filename:
                decoded_filename = self._decode_header(filename)
                if decoded_filename and decoded_filename.strip():
                    logger.info(f"📝 [FILENAME_METHOD_1] Standard method: {decoded_filename}")
                    return decoded_filename

            # Method 2: Parse Content-Disposition header manually
            content_disposition = str(part.get('Content-Disposition', ''))
            if 'filename=' in content_disposition:
                # Extract filename from disposition header
                import re
                filename_match = re.search(r'filename[*]?=([^;]+)', content_disposition)
                if filename_match:
                    raw_filename = filename_match.group(1).strip('"\'')
                    decoded_filename = self._decode_header(raw_filename)
                    logger.info(f"📝 [FILENAME_METHOD_2] Disposition parsing: {decoded_filename}")
                    return decoded_filename

            # Method 3: Generate filename from content type
            if content_type:
                extension_map = {
                    'application/pdf': 'pdf',
                    'application/msword': 'doc',
                    'application/vnd.ms-excel': 'xls',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
                    'image/jpeg': 'jpg',
                    'image/png': 'png',
                    'image/gif': 'gif',
                    'text/plain': 'txt',
                    'text/html': 'html'
                }

                extension = extension_map.get(content_type, content_type.split('/')[-1])
                generated_filename = f"attachment_{part_number}.{extension}"
                logger.info(f"📝 [FILENAME_METHOD_3] Generated from content type: {generated_filename}")
                return generated_filename

            # Method 4: Last resort - generic filename
            fallback_filename = f"attachment_{part_number}.bin"
            logger.info(f"📝 [FILENAME_METHOD_4] Fallback: {fallback_filename}")
            return fallback_filename

        except Exception as e:
            logger.error(f"❌ [FILENAME_ERROR] Error extracting filename: {e}")
            return f"attachment_{part_number}.bin"

    def _extract_payload(self, part) -> bytes:
        """Extract clean payload without email headers mixed in"""
        try:
            # Method 1: Standard decoded payload (most reliable for binary files)
            payload = part.get_payload(decode=True)
            if payload and len(payload) > 0:
                # Check if this looks like clean binary data (no email headers)
                if self._is_clean_binary_data(payload):
                    logger.info(f"💾 [PAYLOAD_METHOD_1] Clean binary payload: {len(payload)} bytes")
                    return payload
                else:
                    logger.warning(f"⚠️ [PAYLOAD_METHOD_1] Payload contains text/headers, trying other methods")

            # Method 2: Raw payload without decoding (for base64 attachments)
            raw_payload = part.get_payload(decode=False)
            if raw_payload and isinstance(raw_payload, str):
                # Check if it looks like base64 data
                if self._looks_like_base64(raw_payload):
                    try:
                        import base64
                        decoded_payload = base64.b64decode(raw_payload)
                        if len(decoded_payload) > 0:
                            # CRITICAL: Check decoded base64 for contamination too
                            if self._is_clean_binary_data(decoded_payload):
                                logger.info(f"💾 [PAYLOAD_METHOD_2] Base64 decode successful and clean: {len(decoded_payload)} bytes")
                                return decoded_payload
                            else:
                                logger.warning(f"⚠️ [PAYLOAD_METHOD_2] Base64 decoded but contains email headers, rejecting")
                    except Exception:
                        pass

                # If not base64, check if it's clean text content
                if not self._contains_email_headers(raw_payload):
                    try:
                        utf8_payload = raw_payload.encode('utf-8')
                        if len(utf8_payload) > 0:
                            # CRITICAL: Check UTF-8 encoded payload for contamination too
                            if self._is_clean_binary_data(utf8_payload):
                                logger.info(f"💾 [PAYLOAD_METHOD_3] Clean text to UTF-8: {len(utf8_payload)} bytes")
                                return utf8_payload
                            else:
                                logger.warning(f"⚠️ [PAYLOAD_METHOD_3] UTF-8 payload contains email headers, rejecting")
                    except Exception:
                        pass
                else:
                    logger.warning(f"⚠️ [PAYLOAD_METHOD_3] Raw payload contains email headers, skipping")

            # Method 3: Handle bytes payload directly
            if raw_payload and isinstance(raw_payload, bytes):
                if self._is_clean_binary_data(raw_payload):
                    logger.info(f"💾 [PAYLOAD_METHOD_4] Clean direct bytes: {len(raw_payload)} bytes")
                    return raw_payload

            # MODIFIED: Upload ALL files to Supabase, even if contaminated - clean during processing
            # Try to get any payload available for upload
            if payload and len(payload) > 0:
                logger.warning(f"⚠️ [PAYLOAD_FALLBACK] Using original payload despite potential contamination: {len(payload)} bytes")
                return payload
            elif raw_payload and isinstance(raw_payload, str):
                try:
                    fallback_payload = raw_payload.encode('utf-8')
                    logger.warning(f"⚠️ [PAYLOAD_FALLBACK] Using raw string payload: {len(fallback_payload)} bytes")
                    return fallback_payload
                except Exception:
                    pass
            elif raw_payload and isinstance(raw_payload, bytes):
                logger.warning(f"⚠️ [PAYLOAD_FALLBACK] Using raw bytes payload: {len(raw_payload)} bytes")
                return raw_payload

            logger.error(f"🚫 [PAYLOAD_EMPTY] No payload found at all")
            return b''

        except Exception as e:
            logger.error(f"❌ [PAYLOAD_ERROR] Error extracting payload: {e}")
            return b''

    def _create_claim_temp_directory(self, claim_id: str) -> str:
        """
        Create organized temporary directory structure: tmp/claimid/files/
        Returns the files directory path
        """
        import tempfile
        import os

        try:
            # Get system temp directory
            base_temp_dir = tempfile.gettempdir()

            # Create organized structure: tmp/claimid/files/
            claim_temp_dir = os.path.join(base_temp_dir, claim_id)
            files_dir = os.path.join(claim_temp_dir, 'files')

            # Create directories if they don't exist
            os.makedirs(files_dir, exist_ok=True)

            logger.info(f"📁 [CLAIM_TEMP_DIR] Created: {files_dir}")
            return files_dir

        except Exception as e:
            logger.error(f"❌ [CLAIM_TEMP_DIR] Error creating temp directory for claim {claim_id}: {e}")
            return None

    def _extract_payload_to_organized_temp_file(self, part, filename: str, claim_id: str) -> str:
        """
        Extract payload to organized temporary file: tmp/claimid/files/filename
        This bypasses email header contamination by writing clean binary data directly
        """
        import os

        try:
            # Create organized temp directory structure
            files_dir = self._create_claim_temp_directory(claim_id)
            if not files_dir:
                return None

            # Create full file path in organized structure
            safe_filename = self._sanitize_filename(filename)
            temp_file_path = os.path.join(files_dir, safe_filename)

            logger.info(f"📁 [ORGANIZED_TEMP] Extracting to: {temp_file_path}")

            # Method 1: Try get_payload(decode=True) - most reliable for binary
            payload = part.get_payload(decode=True)
            if payload and len(payload) > 0:
                # Write binary data directly to organized temp file
                with open(temp_file_path, 'wb') as temp_file:
                    temp_file.write(payload)

                # Verify file was written correctly
                file_size = os.path.getsize(temp_file_path)
                if file_size > 0:
                    logger.info(f"✅ [ORGANIZED_TEMP] Method 1 success: {file_size} bytes written to {temp_file_path}")
                    return temp_file_path
                else:
                    logger.warning(f"⚠️ [ORGANIZED_TEMP] Method 1 produced empty file")
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

            # Method 2: Try base64 decoding if method 1 failed
            raw_payload = part.get_payload(decode=False)
            if raw_payload and isinstance(raw_payload, str) and self._looks_like_base64(raw_payload):
                try:
                    import base64
                    decoded_payload = base64.b64decode(raw_payload)
                    if len(decoded_payload) > 0:
                        with open(temp_file_path, 'wb') as temp_file:
                            temp_file.write(decoded_payload)

                        file_size = os.path.getsize(temp_file_path)
                        if file_size > 0:
                            logger.info(f"✅ [ORGANIZED_TEMP] Method 2 (base64) success: {file_size} bytes written to {temp_file_path}")
                            return temp_file_path
                        else:
                            if os.path.exists(temp_file_path):
                                os.unlink(temp_file_path)
                except Exception as e:
                    logger.warning(f"⚠️ [ORGANIZED_TEMP] Base64 decode failed: {e}")

            # Method 3: Try raw bytes if available
            if raw_payload and isinstance(raw_payload, bytes):
                with open(temp_file_path, 'wb') as temp_file:
                    temp_file.write(raw_payload)

                file_size = os.path.getsize(temp_file_path)
                if file_size > 0:
                    logger.info(f"✅ [ORGANIZED_TEMP] Method 3 (raw bytes) success: {file_size} bytes written to {temp_file_path}")
                    return temp_file_path
                else:
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

            logger.error(f"❌ [ORGANIZED_TEMP] All extraction methods failed for {filename}")
            return None

        except Exception as e:
            logger.error(f"❌ [ORGANIZED_TEMP] Error creating organized temp file for {filename}: {e}")
            return None

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe file system storage"""
        import re

        if not filename:
            return "attachment.bin"

        # Remove or replace unsafe characters
        safe_filename = re.sub(r'[<>:"/\\|?*]', '_', filename)

        # Ensure filename is not too long (max 255 chars for most filesystems)
        if len(safe_filename) > 255:
            name, ext = os.path.splitext(safe_filename)
            safe_filename = name[:255-len(ext)] + ext

        return safe_filename

    def cleanup_claim_temp_files(self, claim_id: str) -> bool:
        """
        Clean up all temporary files for a specific claim ID
        Call this after successful upload to Supabase and OCR processing
        """
        import tempfile
        import shutil
        import os

        try:
            # Get the claim temp directory
            base_temp_dir = tempfile.gettempdir()
            claim_temp_dir = os.path.join(base_temp_dir, claim_id)

            if os.path.exists(claim_temp_dir):
                # Remove entire claim directory and all files
                shutil.rmtree(claim_temp_dir)
                logger.info(f"🧹 [CLEANUP_CLAIM] Successfully removed temp directory: {claim_temp_dir}")
                return True
            else:
                logger.debug(f"🧹 [CLEANUP_CLAIM] Temp directory doesn't exist: {claim_temp_dir}")
                return True

        except Exception as e:
            logger.error(f"❌ [CLEANUP_CLAIM] Error cleaning up temp files for claim {claim_id}: {e}")
            return False

    def get_claim_temp_files_for_ocr(self, claim_id: str) -> List[str]:
        """
        Get list of temp file paths for OCR processing
        Returns list of file paths in tmp/claimid/files/
        """
        import tempfile
        import os

        try:
            # Get the files directory for this claim
            base_temp_dir = tempfile.gettempdir()
            files_dir = os.path.join(base_temp_dir, claim_id, 'files')

            if not os.path.exists(files_dir):
                logger.warning(f"⚠️ [OCR_FILES] Files directory doesn't exist: {files_dir}")
                return []

            # Get all files in the directory
            file_paths = []
            for filename in os.listdir(files_dir):
                file_path = os.path.join(files_dir, filename)
                if os.path.isfile(file_path):
                    file_paths.append(file_path)

            logger.info(f"📁 [OCR_FILES] Found {len(file_paths)} files for OCR processing in {files_dir}")
            return file_paths

        except Exception as e:
            logger.error(f"❌ [OCR_FILES] Error getting temp files for claim {claim_id}: {e}")
            return []

    def _is_clean_binary_data(self, data: bytes) -> bool:
        """Check if data appears to be clean binary file content (not email headers)"""
        try:
            if not data or len(data) < 10:
                return False
            
            # Convert first 500 bytes to string for thorough analysis
            sample = data[:500]
            try:
                sample_str = sample.decode('utf-8', errors='ignore')
            except:
                # If it can't be decoded as text, it's likely binary data (good)
                return True
            
            # Enhanced email header patterns that should NOT be in file content
            email_header_patterns = [
                'Content-Type:', 'Content-Disposition:', 'Content-Transfer-Encoding:',
                'MIME-Version:', 'Message-ID:', 'From:', 'To:', 'Subject:',
                'Date:', 'Return-Path:', 'Received:', 'X-', 'Delivered-To:',
                'Authentication-Results:', 'DKIM-Signature:', 'Received-SPF:',
                'raw_email_header',  # Explicit check for this literal string
                'boundary=',  # MIME boundary indicators
                'charset=',   # Character set declarations
                'Content-ID:', 'Reply-To:', 'CC:', 'BCC:',
                'User-Agent:', 'X-Mailer:', 'Thread-Topic:', 'Thread-Index:',
                'List-Unsubscribe:', 'Precedence:', 'Auto-Submitted:',
                'In-Reply-To:', 'References:'
            ]
            
            sample_lower = sample_str.lower()
            
            # Check for any email header patterns
            for pattern in email_header_patterns:
                if pattern.lower() in sample_lower:
                    logger.warning(f"🚫 [CONTAMINATED_PAYLOAD] Found email header pattern: {pattern}")
                    logger.warning(f"🚫 [CONTAMINATED_PAYLOAD] Content preview: {sample_str[:200]}")
                    return False
            
            # Additional checks for email-like structure
            if '\r\n\r\n' in sample_str:  # Email header/body separator
                logger.warning(f"🚫 [CONTAMINATED_PAYLOAD] Found email header/body separator")
                return False
            
            if sample_str.startswith('Return-Path:') or sample_str.startswith('Received:'):
                logger.warning(f"🚫 [CONTAMINATED_PAYLOAD] Starts with email headers")
                return False
            
            # Check for multiple colons followed by newlines (header pattern)
            import re
            header_like_lines = re.findall(r'^[A-Za-z-]+:\s*.*$', sample_str, re.MULTILINE)
            if len(header_like_lines) > 2:  # More than 2 header-like lines is suspicious
                logger.warning(f"🚫 [CONTAMINATED_PAYLOAD] Found {len(header_like_lines)} header-like lines")
                return False
            
            return True
            
        except Exception:
            # If analysis fails, assume it's binary (safer)
            return True
    
    def _looks_like_base64(self, data: str) -> bool:
        """Check if string looks like base64 encoded data"""
        try:
            if not data or len(data) < 10:
                return False
            
            # Remove whitespace
            clean_data = ''.join(data.split())
            
            # Base64 should only contain A-Z, a-z, 0-9, +, /, =
            import re
            if not re.match(r'^[A-Za-z0-9+/=]+$', clean_data):
                return False
            
            # Length should be multiple of 4 (with padding)
            if len(clean_data) % 4 != 0:
                return False
            
            # Try to decode a sample to verify
            try:
                import base64
                sample = clean_data[:100]  # Test first 100 chars
                base64.b64decode(sample, validate=True)
                return True
            except:
                return False
                
        except Exception:
            return False
    
    def _contains_email_headers(self, text: str) -> bool:
        """Check if text contains email headers"""
        try:
            if not text:
                return False
            
            # Look for common email header patterns
            header_patterns = [
                'Content-Type:', 'Content-Disposition:', 'MIME-Version:',
                'Message-ID:', 'From:', 'To:', 'Subject:', 'Date:',
                'Return-Path:', 'Received:', 'X-Mailer:', 'User-Agent:'
            ]
            
            text_lower = text.lower()
            for pattern in header_patterns:
                if pattern.lower() in text_lower:
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _check_attachments_mentioned(self, body: str) -> bool:
        """Check if attachments are mentioned in email body"""
        if not body:
            return False
            
        attachment_keywords = [
            "attached", "attachment", "please find attached", "enclosed", 
            "document", "file", "report", "pdf", "image", "scan"
        ]
        return any(keyword in body.lower() for keyword in attachment_keywords)
    
    def _generate_email_hash(self, email_data: Dict[str, Any]) -> str:
        """Generate a hash for the email to avoid duplicates"""
        try:
            import hashlib
            
            # Create hash from key email fields
            hash_string = f"{email_data.get('message_id', '')}{email_data.get('from_address', '')}{email_data.get('subject', '')}{email_data.get('date', '')}"
            return hashlib.md5(hash_string.encode()).hexdigest()
            
        except Exception as e:
            logger.error(f"Error generating email hash: {e}")
            return ""
    
    async def _default_callback(self, email_data: Dict[str, Any]):
        """Default callback that forwards to workflow coordinator"""
        if self.workflow_coordinator:
            # Create proper workflow ID format (same as API endpoint)
            message_id = email_data.get('message_id', '')
            workflow_id = f"email_{message_id.strip('<>')}" if message_id else f"email_{datetime.now().isoformat()}"
            
            logger.info(f"🔄 [EMAIL_MONITOR] Creating workflow for email: {email_data.get('subject', 'No subject')}")
            logger.info(f"🆔 [EMAIL_MONITOR] Generated workflow_id: {workflow_id}")
            
            # Use start_workflow instead of process_incoming_email to ensure workflow is created
            await self.workflow_coordinator.start_workflow(
                workflow_id=workflow_id,
                workflow_type="email_processing",
                data=email_data
            )
            
            logger.info(f"✅ [EMAIL_MONITOR] Email workflow created successfully, workflow_id: {workflow_id}")
        else:
            logger.warning("No workflow coordinator configured, email not processed")
    
    async def test_connection(self) -> bool:
        """Test IMAP connection"""
        try:
            mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            mail.login(self.email_address, self.password)
            mail.select('INBOX')
            mail.close()
            mail.logout()
            
            logger.info("Email connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Email connection test failed: {e}")
            return False
    
    async def get_recent_emails(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent emails for testing"""
        try:
            mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            mail.login(self.email_address, self.password)
            mail.select('INBOX')
            
            # Search for recent emails
            status, messages = mail.search(None, 'ALL')
            
            emails = []
            if status == 'OK' and messages[0]:
                email_ids = messages[0].split()[-limit:]  # Get last N emails
                
                for email_id in email_ids:
                    status, msg_data = mail.fetch(email_id, '(RFC822)')
                    if status == 'OK':
                        email_body = msg_data[0][1]
                        email_message = email.message_from_bytes(email_body)
                        email_data = await self._extract_email_data(email_message)
                        emails.append(email_data)
            
            mail.close()
            mail.logout()
            
            return emails
            
        except Exception as e:
            logger.error(f"Error getting recent emails: {e}")
            return []
    
    def get_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        return {
            "status": "running" if self.is_monitoring else "stopped",
            "service": "email_monitor",
            "email_address": self.email_address,
            "processed_count": len(self.processed_emails),
            "version": "2.0.0"
        }

    def clean_attachment_for_ocr(self, file_content: bytes, filename: str) -> bytes:
        """
        Clean email headers from attachment content before OCR processing
        This is called by the OCR service to clean contaminated files
        """
        try:
            if not file_content or len(file_content) < 10:
                return file_content

            logger.info(f"🧹 [OCR_CLEAN] Cleaning attachment for OCR: {filename} ({len(file_content)} bytes)")

            # Check if this is likely a binary file first
            if self._is_likely_binary_file(file_content, filename):
                logger.info(f"✅ [OCR_CLEAN] Binary file detected, no cleaning needed: {filename}")
                return file_content

            # Try to decode as text to check for contamination
            try:
                text_content = file_content.decode('utf-8', errors='ignore')
            except:
                # If it can't be decoded, it's likely binary - return as is
                logger.info(f"✅ [OCR_CLEAN] Cannot decode as text, treating as binary: {filename}")
                return file_content

            # Check if this looks like email headers at the start
            lines = text_content.split('\n')
            clean_lines = []
            header_section_ended = False
            headers_removed = 0

            for i, line in enumerate(lines):
                line_stripped = line.strip()

                # Skip empty lines at the start
                if not line_stripped and not header_section_ended:
                    continue

                # Check if this line looks like an email header
                if not header_section_ended and self._is_email_header_line(line_stripped):
                    logger.debug(f"🧹 [OCR_CLEAN] Removing email header: {line_stripped[:50]}...")
                    headers_removed += 1
                    continue

                # Check for MIME boundary markers
                if 'boundary=' in line_stripped or line_stripped.startswith('--'):
                    logger.debug(f"🧹 [OCR_CLEAN] Removing MIME boundary: {line_stripped[:50]}...")
                    headers_removed += 1
                    continue

                # Check for the literal "raw_email_header" contamination
                if 'raw_email_header' in line_stripped.lower():
                    logger.info(f"🧹 [OCR_CLEAN] Removing raw_email_header contamination from {filename}")
                    headers_removed += 1
                    continue

                # If we get here, this line appears to be actual content
                header_section_ended = True
                clean_lines.append(line)

            # Reconstruct the cleaned content
            if clean_lines:
                cleaned_text = '\n'.join(clean_lines)
                cleaned_bytes = cleaned_text.encode('utf-8')

                reduction_percent = ((len(file_content) - len(cleaned_bytes))/len(file_content)*100)

                if headers_removed > 0:
                    logger.info(f"✅ [OCR_CLEAN] Cleaned {headers_removed} email headers from {filename} ({reduction_percent:.1f}% reduction)")
                else:
                    logger.info(f"✅ [OCR_CLEAN] No email headers found in {filename}")

                return cleaned_bytes
            else:
                # If all lines were removed, return original data
                logger.warning(f"⚠️ [OCR_CLEAN] All content was removed during cleaning, returning original: {filename}")
                return file_content

        except Exception as e:
            logger.error(f"❌ [OCR_CLEAN] Error cleaning email headers from {filename}: {e}")
            return file_content

    def _is_email_header_line(self, line: str) -> bool:
        """Check if a line looks like an email header"""
        if not line:
            return False

        # Common email header patterns
        header_patterns = [
            'Content-Type:', 'Content-Disposition:', 'Content-Transfer-Encoding:',
            'MIME-Version:', 'Message-ID:', 'From:', 'To:', 'Subject:',
            'Date:', 'Return-Path:', 'Received:', 'Delivered-To:',
            'Authentication-Results:', 'DKIM-Signature:', 'Received-SPF:',
            'Content-ID:', 'Reply-To:', 'CC:', 'BCC:', 'User-Agent:',
            'X-Mailer:', 'Thread-Topic:', 'Thread-Index:', 'List-Unsubscribe:',
            'Precedence:', 'Auto-Submitted:', 'In-Reply-To:', 'References:'
        ]

        line_lower = line.lower()

        # Check for exact header matches
        for pattern in header_patterns:
            if line_lower.startswith(pattern.lower()):
                return True

        # Check for X- headers (custom headers)
        if line_lower.startswith('x-') and ':' in line:
            return True

        # Check for header-like pattern (word: value)
        if ':' in line and len(line.split(':')) == 2:
            header_name = line.split(':')[0].strip()
            # If it's all caps or title case and contains no spaces, likely a header
            if header_name.isupper() or (header_name.istitle() and ' ' not in header_name):
                return True

        return False

    def _is_likely_binary_file(self, file_content: bytes, filename: str) -> bool:
        """Check if file is likely binary based on content and filename"""
        try:
            # Check file extension first
            if filename:
                binary_extensions = [
                    '.pdf', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif',
                    '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
                    '.zip', '.rar', '.7z', '.tar', '.gz',
                    '.mp3', '.mp4', '.avi', '.mov', '.wav',
                    '.exe', '.dll', '.so', '.dylib'
                ]

                filename_lower = filename.lower()
                for ext in binary_extensions:
                    if filename_lower.endswith(ext):
                        return True

            # Check for binary file signatures (magic numbers)
            if len(file_content) >= 4:
                # PDF signature
                if file_content.startswith(b'%PDF'):
                    return True

                # Image signatures
                if (file_content.startswith(b'\x89PNG') or  # PNG
                    file_content.startswith(b'\xFF\xD8\xFF') or  # JPEG
                    file_content.startswith(b'GIF8') or  # GIF
                    file_content.startswith(b'BM')):  # BMP
                    return True

                # ZIP/Office documents
                if file_content.startswith(b'PK\x03\x04'):  # ZIP/Office
                    return True

                # MS Office old format
                if file_content.startswith(b'\xD0\xCF\x11\xE0'):  # MS Office
                    return True

            # Check for high percentage of non-printable characters
            if len(file_content) > 100:
                sample = file_content[:500]
                non_printable = sum(1 for b in sample if b < 32 and b not in [9, 10, 13])
                if non_printable / len(sample) > 0.3:  # More than 30% non-printable
                    return True

            return False

        except Exception:
            # If analysis fails, assume it's text (safer for cleaning)
            return False
