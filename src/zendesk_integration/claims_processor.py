"""
🏭 Claims Processing Service - Zendesk + Supabase Integration

Comprehensive service that orchestrates:
- Claim creation in Supabase database
- Attachment processing and storage
- AI-enhanced Zendesk ticket creation
- Complete audit trail and error handling
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
import uuid
import json

import structlog
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.base import MIMEBase

from ..config.settings import Settings
from ..database.supabase_client import SupabaseClient
from .zendesk_client import ZendeskClient
from ..utils.dozzle_logger import dozzle_log

logger = structlog.get_logger(__name__)


class ClaimsProcessor:
    """
    Comprehensive Claims Processing Service
    
    Orchestrates the complete flow from email classification to Zendesk ticket creation:
    1. Create claim record in Supabase
    2. Process and store attachments
    3. Create AI-enhanced Zendesk ticket
    4. Maintain complete audit trail
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)
        self.zendesk = ZendeskClient(settings, self.supabase)
    
    async def process_claim_email(self,
                                workflow_id: str,
                                email_data: Dict[str, Any],
                                classification_result: Dict[str, Any],
                                attachments: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Complete claim processing workflow
        
        Args:
            workflow_id: Workflow ID from email processing
            email_data: Original email information
            classification_result: AI analysis results
            attachments: List of email attachments (optional)
            
        Returns:
            Complete processing result with claim_id, ticket_id, and status
        """
        claim_id = None
        processing_result = {
            'success': False,
            'claim_id': None,
            'zendesk_ticket_id': None,
            'zendesk_ticket_url': None,
            'attachments_processed': 0,
            'errors': []
        }
        
        try:
            dozzle_log("info", "🏭 [CLAIMS_PROCESSOR] Starting claim processing workflow",
                      workflow_id=workflow_id,
                      sender=email_data.get('sender_email'),
                      has_attachments=bool(attachments))
            
            # Step 1: Create claim record in Supabase FIRST to get claim_id
            claim_id = await self._create_claim_record(
                workflow_id, email_data, classification_result,
                zendesk_ticket_id=None,  # Will be updated after ticket creation
                zendesk_ticket_url=None
            )
            processing_result['claim_id'] = claim_id

            dozzle_log("info", "📝 [CLAIMS_PROCESSOR] Claim record created",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      claim_type=classification_result.get('claim_type'))

            # Step 2: Create professional Zendesk ticket WITH the actual claim_id
            ticket_result = await self._create_zendesk_ticket(
                claim_id, workflow_id, email_data, classification_result, []  # Now we have claim_id
            )

            processing_result.update({
                'zendesk_ticket_id': ticket_result.get('id'),  # Zendesk API returns 'id', not 'ticket_id'
                'zendesk_ticket_url': ticket_result.get('url'),  # Zendesk API returns 'url', not 'ticket_url'
                'zendesk_priority': ticket_result.get('priority'),
                'ai_priority_score': ticket_result.get('priority_score', 0.0)  # Default if not present
            })

            dozzle_log("info", "🎫 [CLAIMS_PROCESSOR] Professional Zendesk ticket created successfully",
                      workflow_id=workflow_id,
                      ticket_id=ticket_result.get('id'),
                      ticket_url=ticket_result.get('url'))

            # Step 2.1: Add AI analysis as second comment
            await self.zendesk.add_ai_analysis_comment(
                ticket_id=ticket_result.get('id'),
                classification_result=classification_result,
                claim_id=claim_id,
                email_data=email_data,
                attachments=attachments
            )

            # Step 2.5: Update claim record with Zendesk ticket information
            await self.supabase.update_claim(
                claim_id=claim_id,
                updates={
                    'zendesk_ticket_id': ticket_result.get('id'),
                    'zendesk_ticket_url': ticket_result.get('url')
                }
            )

            # Step 3: Process attachments if present and update Zendesk ticket
            processed_attachments = []
            if attachments:
                processed_attachments = await self._process_attachments(claim_id, workflow_id, attachments)
                processing_result['attachments_processed'] = len(processed_attachments)

                # Update Zendesk ticket with attachment information
                await self._update_zendesk_ticket_with_attachments(
                    ticket_result.get('id'), processed_attachments
                )
            
            # Step 4: Process documents through OCR if attachments exist
            ocr_results = {}
            if processed_attachments:
                try:
                    dozzle_log("info", "🔍 [CLAIMS_PROCESSOR] Starting OCR processing",
                              claim_id=claim_id,
                              attachment_count=len(processed_attachments))
                    
                    # Import OCR service
                    from ..ocr_consensus.ocr_service import ZurichOCRService
                    
                    # Create OCR service with Zendesk client for comments
                    ocr_service = ZurichOCRService(
                        settings=self.settings,
                        supabase_client=self.supabase,
                        zendesk_client=self.zendesk
                    )
                    
                    # Process all attachments through OCR
                    ocr_results = await ocr_service.process_claim_attachments(
                        claim_id=claim_id,
                        zendesk_ticket_id=ticket_result.get('id'),
                        workflow_id=workflow_id
                    )
                    
                    processing_result['ocr_processed'] = ocr_results.get('processed_count', 0)
                    processing_result['ocr_total'] = ocr_results.get('total_count', 0)
                    processing_result['ocr_status'] = ocr_results.get('status', 'unknown')
                    
                    dozzle_log("info", "✅ [CLAIMS_PROCESSOR] OCR processing completed",
                              claim_id=claim_id,
                              processed_count=ocr_results.get('processed_count', 0),
                              total_count=ocr_results.get('total_count', 0),
                              status=ocr_results.get('status'))
                    
                except Exception as ocr_error:
                    dozzle_log("error", "❌ [CLAIMS_PROCESSOR] OCR processing failed",
                              claim_id=claim_id,
                              error=str(ocr_error))
                    # Don't fail the whole workflow if OCR fails
                    processing_result['ocr_error'] = str(ocr_error)
                    processing_result['ocr_status'] = 'failed'
            else:
                dozzle_log("info", "📄 [CLAIMS_PROCESSOR] No attachments to process through OCR",
                          claim_id=claim_id)
                processing_result['ocr_status'] = 'no_attachments'
            
            # Step 5: Add completion to audit trail
            await self.supabase.add_claim_history(
                claim_id=claim_id,
                event_type="claim_processing_completed",
                description="Complete claim processing workflow finished successfully",
                new_values={
                    'status': 'processing',
                    'zendesk_ticket_id': ticket_result.get('id'),
                    'attachments_count': len(processed_attachments),
                    'ocr_processed': processing_result.get('ocr_processed', 0),
                    'ocr_status': processing_result.get('ocr_status', 'unknown')
                },
                metadata={
                    'workflow_id': workflow_id,
                    'processing_duration': 'completed',
                    'ai_priority_score': ticket_result.get('priority_score', 0),  # Safe access with default
                    'ocr_results': ocr_results if ocr_results else None
                }
            )
            
            processing_result['success'] = True
            
            dozzle_log("info", "✅ [CLAIMS_PROCESSOR] Claim processing completed successfully",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      zendesk_ticket_id=ticket_result.get('id'),
                      attachments_processed=len(processed_attachments),
                      priority=ticket_result.get('priority'))
            
            return processing_result
            
        except Exception as e:
            error_msg = f"Claim processing failed: {str(e)}"
            processing_result['errors'].append(error_msg)
            
            dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Claim processing failed",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      error=str(e))
            
            # Add error to audit trail if claim was created
            if claim_id:
                try:
                    await self.supabase.add_claim_history(
                        claim_id=claim_id,
                        event_type="claim_processing_failed",
                        description=error_msg,
                        metadata={
                            'workflow_id': workflow_id,
                            'error': str(e),
                            'processing_step': 'unknown'
                        }
                    )
                    
                    # Update claim status to indicate failure
                    await self.supabase.update_claim(claim_id, {
                        'status': 'failed'
                    })
                except:
                    pass  # Don't fail if audit logging fails
            
            raise
    
    async def _create_claim_record(self,
                                 workflow_id: str,
                                 email_data: Dict[str, Any],
                                 classification_result: Dict[str, Any],
                                 zendesk_ticket_id: str = None,
                                 zendesk_ticket_url: str = None) -> str:
        """
        Create comprehensive claim record in Supabase
        
        Args:
            workflow_id: Workflow ID from email processing
            email_data: Original email information
            classification_result: AI analysis results
            
        Returns:
            Created claim_id (UUID)
        """
        try:
            dozzle_log("info", "📝 [CLAIMS_PROCESSOR] Creating claim record",
                      workflow_id=workflow_id)
            
            # Extract AI analysis results
            final_analysis = classification_result.get('final_analysis', {})
            extracted_details = final_analysis.get('extracted_details', {})
            
            # Build comprehensive claim data with validation
            sender_email = email_data.get('sender_email', '').strip()
            if not sender_email:
                raise ValueError("Sender email is required for claim creation")
            
            # Extract sender name with better logic
            sender_name = email_data.get('sender_name', '') or email_data.get('from_name', '')
            if not sender_name and sender_email:
                # Generate name from email if not provided
                name_part = sender_email.split('@')[0]
                sender_name = name_part.replace('.', ' ').replace('_', ' ').title()
            
            # Clean and validate email subject
            email_subject = email_data.get('subject', '').strip()
            if not email_subject:
                email_subject = f"Claim Submission from {sender_name}"
            
            claim_data = {
                'workflow_id': workflow_id,
                'email_subject': email_subject,
                'email_body': email_data.get('body', '').strip(),
                'sender_email': sender_email,
                'sender_name': sender_name,
                'received_at': email_data.get('received_at', datetime.utcnow().isoformat()),
                
                # AI Classification Results
                'claim_type': final_analysis.get('claim_type', 'general'),
                'urgency_level': final_analysis.get('urgency_level', 'medium'),
                'confidence_level': final_analysis.get('confidence', 'medium'),
                'ai_classification_result': classification_result,
                'consensus_confidence': classification_result.get('consensus_confidence', 0.0),
                
                # Extracted claim details
                'policy_number': extracted_details.get('policy_number'),
                'incident_date': extracted_details.get('incident_date'),
                'incident_location': extracted_details.get('incident_location'),
                'estimated_value': extracted_details.get('estimated_value'),
                'claimant_name': extracted_details.get('claimant_name'),
                'claimant_phone': extracted_details.get('claimant_phone'),

                # Zendesk Integration (if provided)
                'zendesk_ticket_id': zendesk_ticket_id,
                'zendesk_ticket_url': zendesk_ticket_url,

                # Initial status
                'status': 'processing' if zendesk_ticket_id else 'received'
            }
            
            # Create claim record
            created_claim = await self.supabase.create_claim(claim_data)
            claim_id = created_claim['id']  # Database uses 'id' as primary key
            
            # Add initial history entry
            await self.supabase.add_claim_history(
                claim_id=claim_id,
                event_type="claim_created",
                description="Claim record created from AI-classified email",
                new_values=claim_data,
                metadata={
                    'workflow_id': workflow_id,
                    'ai_models_used': ['gpt-4o', 'gpt-4o-mini'],
                    'consensus_confidence': classification_result.get('consensus_confidence')
                }
            )

            return claim_id
            
        except Exception as e:
            dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Failed to create claim record",
                      workflow_id=workflow_id,
                      error=str(e))
            raise
    
    async def _process_attachments(self,
                                 claim_id: str,
                                 workflow_id: str,
                                 attachments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process and store email attachments in Supabase Storage
        
        Args:
            claim_id: UUID of the claim
            workflow_id: Workflow ID for organization
            attachments: List of attachment data
            
        Returns:
            List of processed attachment records
        """
        processed_attachments = []
        
        try:
            dozzle_log("info", "📎 [CLAIMS_PROCESSOR] Processing attachments",
                      claim_id=claim_id,
                      attachment_count=len(attachments))
            
            for i, attachment in enumerate(attachments):
                try:
                    # Extract attachment information with multiple possible field names
                    filename = attachment.get('filename', attachment.get('name', f'attachment_{i+1}'))
                    content_type = attachment.get('content_type', attachment.get('contentType', 'application/octet-stream'))

                    # Try multiple possible content field names
                    file_content = (
                        attachment.get('content') or
                        attachment.get('data') or
                        attachment.get('file_content') or
                        attachment.get('body') or
                        attachment.get('source') or  # Common in email parsing libraries
                        attachment.get('raw_content') or
                        attachment.get('payload') or
                        b''
                    )
                    
                    # Additional handling for different content types
                    if file_content and isinstance(file_content, str):
                        # If content is string, try to decode as base64 or convert to bytes
                        try:
                            import base64
                            # Try base64 decode first
                            file_content = base64.b64decode(file_content)
                        except:
                            # If not base64, encode as UTF-8
                            file_content = file_content.encode('utf-8')

                    # Enhanced debug log for attachment structure
                    content_size = len(file_content) if file_content else 0
                    content_preview = str(file_content)[:100] + "..." if file_content and len(str(file_content)) > 100 else str(file_content)
                    
                    dozzle_log("info", "🔍 [CLAIMS_PROCESSOR] Processing attachment",
                              filename=filename,
                              content_type=content_type,
                              attachment_keys=list(attachment.keys()),
                              has_content=bool(file_content),
                              content_size=content_size,
                              content_type_detected=type(file_content).__name__,
                              content_preview=content_preview[:50] + "..." if len(content_preview) > 50 else content_preview)
                    
                    # Check specific content fields for debugging
                    for field in ['content', 'data', 'source', 'file_content', 'body', 'raw_content', 'payload']:
                        field_value = attachment.get(field)
                        if field_value is not None:
                            field_size = len(field_value) if hasattr(field_value, '__len__') else 0
                            dozzle_log("debug", f"📎 [CLAIMS_PROCESSOR] Found attachment field: {field}",
                                      field_name=field,
                                      field_type=type(field_value).__name__,
                                      field_size=field_size,
                                      has_value=bool(field_value))

                    if not file_content:
                        dozzle_log("warning", "⚠️ [CLAIMS_PROCESSOR] Skipping empty attachment",
                                  filename=filename,
                                  available_keys=list(attachment.keys()))
                        continue
                    
                    # Upload to Supabase Storage
                    attachment_record = await self.supabase.upload_attachment(
                        claim_id=claim_id,
                        workflow_id=workflow_id,
                        file_content=file_content,
                        filename=filename,
                        content_type=content_type
                    )
                    
                    processed_attachments.append(attachment_record)
                    
                    dozzle_log("info", "✅ [CLAIMS_PROCESSOR] Attachment processed",
                              attachment_id=attachment_record.get('id', attachment_record.get('attachment_id')),
                              filename=filename,
                              size=len(file_content))
                    
                except Exception as e:
                    dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Failed to process attachment",
                              claim_id=claim_id,
                              filename=attachment.get('filename', 'unknown'),
                              error=str(e))
                    # Continue processing other attachments
                    continue
            
            # Add attachment processing to audit trail
            if processed_attachments:
                await self.supabase.add_claim_history(
                    claim_id=claim_id,
                    event_type="attachments_processed",
                    description=f"Processed {len(processed_attachments)} attachments",
                    new_values={
                        'attachments_count': len(processed_attachments),
                        'attachment_ids': [att.get('id', att.get('attachment_id')) for att in processed_attachments]
                    },
                    metadata={
                        'workflow_id': workflow_id,
                        'total_size': sum(att['file_size'] for att in processed_attachments)
                    }
                )
            
            dozzle_log("info", "✅ [CLAIMS_PROCESSOR] All attachments processed",
                      claim_id=claim_id,
                      processed_count=len(processed_attachments),
                      total_count=len(attachments))
            
            return processed_attachments
            
        except Exception as e:
            dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Attachment processing failed",
                      claim_id=claim_id,
                      error=str(e))
            raise
    
    async def _create_zendesk_ticket(self,
                                   claim_id: Optional[str],
                                   workflow_id: str,
                                   email_data: Dict[str, Any],
                                   classification_result: Dict[str, Any],
                                   attachments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create professional Zendesk ticket with clean formatting for insurance claims

        Args:
            claim_id: UUID of the claim (can be None if creating ticket first)
            workflow_id: Workflow ID from email processing
            email_data: Original email information
            classification_result: AI analysis results
            attachments: List of processed attachments

        Returns:
            Zendesk ticket creation result
        """
        try:
            dozzle_log("info", "🎫 [CLAIMS_PROCESSOR] Creating professional Zendesk ticket",
                      claim_id=claim_id,
                      workflow_id=workflow_id)
            
            # Create professional ticket with improved formatting
            ticket_result = await self.zendesk.create_simple_ticket(
                claim_id=claim_id,
                workflow_id=workflow_id,
                email_data=email_data,
                attachments=attachments
            )
            
            dozzle_log("info", "✅ [CLAIMS_PROCESSOR] Professional Zendesk ticket created",
                      claim_id=claim_id,
                      ticket_id=ticket_result.get('id'),
                      priority=ticket_result.get('priority'))
            
            return ticket_result

        except Exception as e:
            dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Professional Zendesk ticket creation failed",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      error=str(e))
            raise

    async def _update_zendesk_ticket_with_attachments(self,
                                                    ticket_id: str,
                                                    processed_attachments: List[Dict[str, Any]]) -> None:
        """
        Create a Zendesk comment with Supabase storage file URLs after successful upload

        Args:
            ticket_id: Zendesk ticket ID
            processed_attachments: List of processed attachment records from Supabase
        """
        try:
            if not processed_attachments:
                dozzle_log("info", "📎 [CLAIMS_PROCESSOR] No attachments to add to Zendesk ticket",
                          ticket_id=ticket_id)
                return

            dozzle_log("info", "📎 [CLAIMS_PROCESSOR] Creating Zendesk comment with Supabase file URLs",
                      ticket_id=ticket_id,
                      attachment_count=len(processed_attachments))

            # Create professional attachment comment with Supabase URLs
            attachment_comment = f"""DOCUMENT STORAGE CONFIRMATION ({len(processed_attachments)} files uploaded)

Files Successfully Uploaded to Secure Storage:

"""

            total_size = 0
            
            for i, attachment in enumerate(processed_attachments, 1):
                filename = attachment.get('original_filename', attachment.get('filename', f'attachment_{i}'))
                file_size = attachment.get('file_size', 0)
                content_type = attachment.get('content_type', 'Unknown')
                storage_url = attachment.get('storage_url')
                
                total_size += file_size
                
                # Format the attachment entry
                attachment_comment += f"""{i}. {filename}
   Size: {self._format_file_size(file_size)}
   Type: {content_type}
   Status: Securely Stored
"""
                
                # Add download link if available
                if storage_url:
                    attachment_comment += f"   Access: {storage_url}\n"
                else:
                    attachment_comment += f"   Access: Link generating...\n"
                
                attachment_comment += "\n"

            # Add summary section
            attachment_comment += f"""---

Total Files: {len(processed_attachments)}
Total Size: {self._format_file_size(total_size)}
Storage Location: Supabase Secure Cloud Storage

All documents have been securely stored and are accessible via the links provided above.
Files will remain available throughout the claims processing period."""

            # Add professional comment to ticket (internal)
            comment_result = await self.zendesk.add_comment_to_ticket(
                ticket_id=ticket_id,
                comment=attachment_comment,
                public=False  # Internal comment for agent use
            )

            if comment_result:
                dozzle_log("info", "✅ [CLAIMS_PROCESSOR] Zendesk comment created with Supabase file URLs",
                          ticket_id=ticket_id,
                          attachment_count=len(processed_attachments),
                          total_size=self._format_file_size(total_size),
                          urls_included=sum(1 for a in processed_attachments if a.get('storage_url')))
            else:
                dozzle_log("warning", "⚠️ [CLAIMS_PROCESSOR] Failed to create Zendesk comment with file URLs",
                          ticket_id=ticket_id)

        except Exception as e:
            dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Failed to create Zendesk comment with file URLs",
                      ticket_id=ticket_id,
                      error=str(e))
            # Don't raise - this is not critical to the main workflow

    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"
