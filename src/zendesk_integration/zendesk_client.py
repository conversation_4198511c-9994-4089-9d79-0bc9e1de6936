"""
🎫 Zendesk Integration for Zurich Claims Processing

AI-Enhanced Zendesk ticket creation with:
- Intelligent priority calculation based on AI analysis
- Rich ticket descriptions with claim context
- Attachment handling with Supabase Storage links
- Comprehensive error handling and retry logic
- Real-time sync with database tracking
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
import json
import time

import requests
import base64
import structlog
import uuid

from ..config.settings import Settings
from ..utils.dozzle_logger import dozzle_log
from ..database.supabase_client import SupabaseClient
from ..communications.email_service import ProfessionalEmailService, EmailThreadInfo

logger = structlog.get_logger(__name__)


class ZendeskClient:
    """
    AI-Enhanced Zendesk Integration for Claims Processing using Direct HTTP API

    Features:
    - Direct HTTP API calls (no zenpy dependency)
    - Intelligent ticket creation with AI context
    - Priority calculation based on claim analysis
    - Attachment handling with Supabase Storage
    - Comprehensive error handling and retry logic
    - Database synchronization and audit trail
    """

    def __init__(self, settings: Settings, supabase_client: SupabaseClient):
        self.settings = settings
        self.supabase = supabase_client
        self.base_url = f"{self.settings.zendesk_url}/api/v2"
        self.session = requests.Session()
        self._use_direct_api = True  # Always use direct API now
        self.email_service = ProfessionalEmailService(settings)
        self._initialize_client()
        
        # Business Status Mapping - Technical to Business Terms
        self.business_status_map = {
            'NEW': 'Claim Received',
            'REVIEW': 'Under Initial Review', 
            'INVESTIGATION': 'Under Investigation',
            'HUMAN_REVIEW': 'Executive Review',
            'DECISION': 'Final Assessment',
            'COMPLETED': 'Claim Processed',
            'APPROVED': 'Claim Approved',
            'DENIED': 'Claim Denied',
            'PENDING_DOCUMENTS': 'Document Validation',
            'WAITING_CUSTOMER': 'Awaiting Customer Response'
        }
        
        # Business Timeline Checkpoints
        self.business_checkpoints = [
            {
                'step': 1,
                'status': 'Claim Received',
                'description': 'We have successfully received your claim submission and supporting documents.',
                'business_term': 'Claim Received'
            },
            {
                'step': 2, 
                'status': 'Document Validation',
                'description': 'Our team is verifying all submitted documents for completeness and accuracy.',
                'business_term': 'Document Validation'
            },
            {
                'step': 3,
                'status': 'Under Investigation', 
                'description': 'We are conducting a thorough assessment of your claim details and circumstances.',
                'business_term': 'Claim Assessment'
            },
            {
                'step': 4,
                'status': 'Executive Review',
                'description': 'Your claim is under final review by our senior claims executive for approval.',
                'business_term': 'Executive Review'
            },
            {
                'step': 5,
                'status': 'Claim Processed',
                'description': 'Your claim has been processed and finalized. Settlement details will follow.',
                'business_term': 'Settlement Processing'
            }
        ]
    
    def _initialize_client(self):
        """Initialize Zendesk client with direct HTTP API authentication"""
        try:
            # Use the zendesk_url property which constructs the full URL
            self.base_url = f"{self.settings.zendesk_url}/api/v2"

            # Debug logging for authentication parameters
            dozzle_log("info", "🔧 [ZENDESK] Initializing direct HTTP API client",
                      subdomain=self.settings.zendesk_subdomain,
                      email=self.settings.zendesk_email,
                      token_length=len(self.settings.zendesk_token) if self.settings.zendesk_token else 0,
                      token_preview=f"{self.settings.zendesk_token[:10]}..." if self.settings.zendesk_token else "None",
                      base_url=self.base_url)

            # Set up authentication headers using Basic Auth
            auth_string = f"{self.settings.zendesk_email}/token:{self.settings.zendesk_token}"
            auth_bytes = base64.b64encode(auth_string.encode()).decode()

            self.session.headers.update({
                "Authorization": f"Basic {auth_bytes}",
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "Zurich-Claims-Processing/1.0"
            })

            # Test authentication immediately
            self._test_authentication()

            dozzle_log("info", "✅ [ZENDESK] Direct HTTP API client initialization completed",
                      using_direct_api=True)

        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to initialize direct HTTP client",
                      error=str(e),
                      error_type=type(e).__name__)
            raise

    def _test_authentication(self):
        """Test authentication by calling the /users/me endpoint"""
        try:
            dozzle_log("info", "🔍 [ZENDESK] Testing authentication with /users/me endpoint")

            response = self.session.get(f"{self.base_url}/users/me.json", timeout=30)

            dozzle_log("info", "📡 [ZENDESK] Authentication response received",
                      status_code=response.status_code,
                      response_length=len(response.text))

            if response.status_code == 200:
                user_data = response.json()
                user = user_data.get('user', {})
                dozzle_log("info", "✅ [ZENDESK] Direct HTTP API authentication successful",
                          user_id=user.get('id'),
                          name=user.get('name'),
                          email=user.get('email'),
                          active=user.get('active'),
                          role=user.get('role'))
                return True
            else:
                error_text = response.text[:500] if response.text else "No response text"
                dozzle_log("error", "❌ [ZENDESK] Direct HTTP API authentication failed",
                          status_code=response.status_code,
                          response_text=error_text)
                raise Exception(f"Authentication failed: {response.status_code} - {error_text}")

        except requests.exceptions.RequestException as e:
            dozzle_log("error", "❌ [ZENDESK] Network error during authentication test",
                      error=str(e),
                      error_type=type(e).__name__)
            raise
        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Unexpected error during authentication test",
                      error=str(e),
                      error_type=type(e).__name__)
            raise
    
    def _calculate_ai_priority(self, classification_result: Dict[str, Any]) -> Tuple[str, int]:
        """
        Calculate intelligent priority based on AI analysis
        
        Args:
            classification_result: AI classification results
            
        Returns:
            Tuple of (zendesk_priority, priority_score)
        """
        try:
            # Extract key factors from AI analysis
            urgency = classification_result.get('final_analysis', {}).get('urgency_level', 'medium')
            confidence = classification_result.get('consensus_confidence', 0.5)
            claim_type = classification_result.get('final_analysis', {}).get('claim_type', 'general')
            estimated_value = classification_result.get('final_analysis', {}).get('estimated_value', 0)
            
            # Base priority score calculation
            priority_score = 50  # Base score
            
            # Urgency factor (0-30 points)
            urgency_scores = {
                'critical': 30,
                'high': 20,
                'medium': 10,
                'low': 0
            }
            priority_score += urgency_scores.get(urgency.lower(), 10)
            
            # Confidence factor (0-20 points)
            priority_score += int(confidence * 20)
            
            # Claim type factor (0-20 points)
            high_priority_types = ['liability', 'workers_comp', 'auto']
            if claim_type.lower() in high_priority_types:
                priority_score += 15
            elif claim_type.lower() in ['property']:
                priority_score += 10
            else:
                priority_score += 5
            
            # Estimated value factor (0-30 points)
            if estimated_value > 100000:
                priority_score += 30
            elif estimated_value > 50000:
                priority_score += 20
            elif estimated_value > 10000:
                priority_score += 10
            else:
                priority_score += 5
            
            # Convert to Zendesk priority
            if priority_score >= 90:
                zendesk_priority = "urgent"
            elif priority_score >= 70:
                zendesk_priority = "high"
            elif priority_score >= 40:
                zendesk_priority = "normal"
            else:
                zendesk_priority = "low"
            
            dozzle_log("info", "🎯 [ZENDESK] AI priority calculated",
                      urgency=urgency,
                      confidence=confidence,
                      claim_type=claim_type,
                      estimated_value=estimated_value,
                      priority_score=priority_score,
                      zendesk_priority=zendesk_priority)
            
            return zendesk_priority, priority_score
            
        except Exception as e:
            dozzle_log("warning", "⚠️ [ZENDESK] Priority calculation failed, using default",
                      error=str(e))
            return "normal", 50
    
    def _generate_ai_enhanced_description(self, 
                                        email_data: Dict[str, Any],
                                        classification_result: Dict[str, Any],
                                        attachments: List[Dict[str, Any]] = None) -> str:
        """
        Generate comprehensive ticket description with AI insights
        
        Args:
            email_data: Original email information
            classification_result: AI analysis results
            attachments: List of attachment information
            
        Returns:
            Rich HTML description for Zendesk ticket
        """
        try:
            final_analysis = classification_result.get('final_analysis', {})
            consensus_data = classification_result.get('consensus_data', {})
            
            # Build comprehensive description
            description_parts = []
            
            # Header with AI classification
            description_parts.append("🤖 **AI-Enhanced Claim Processing**")
            description_parts.append("=" * 50)
            description_parts.append("")
            
            # Original email information
            description_parts.append("📧 **Original Email Information**")
            description_parts.append(f"**From:** {email_data.get('sender_email', 'Unknown')}")
            description_parts.append(f"**Subject:** {email_data.get('subject', 'No Subject')}")
            description_parts.append(f"**Received:** {email_data.get('received_at', datetime.utcnow().isoformat())}")
            description_parts.append("")
            
            # AI Classification Results
            description_parts.append("🎯 **AI Classification Results**")
            description_parts.append(f"**Claim Type:** {final_analysis.get('claim_type', 'Unknown')}")
            description_parts.append(f"**Urgency Level:** {final_analysis.get('urgency_level', 'Medium')}")
            description_parts.append(f"**Confidence Level:** {final_analysis.get('confidence_level', 'Medium')}")
            description_parts.append(f"**Consensus Confidence:** {classification_result.get('consensus_confidence', 0.0):.1%}")
            description_parts.append("")
            
            # Extracted claim details
            if final_analysis.get('extracted_details'):
                details = final_analysis['extracted_details']
                description_parts.append("📋 **Extracted Claim Details**")
                
                if details.get('policy_number'):
                    description_parts.append(f"**Policy Number:** {details['policy_number']}")
                if details.get('incident_date'):
                    description_parts.append(f"**Incident Date:** {details['incident_date']}")
                if details.get('incident_location'):
                    description_parts.append(f"**Incident Location:** {details['incident_location']}")
                if details.get('claimant_name'):
                    description_parts.append(f"**Claimant:** {details['claimant_name']}")
                if details.get('estimated_value'):
                    description_parts.append(f"**Estimated Value:** ${details['estimated_value']:,.2f}")
                
                description_parts.append("")
            
            # Model consensus information
            if consensus_data:
                description_parts.append("🤖 **AI Model Consensus**")
                description_parts.append(f"**Primary Model (GPT-4o):** {consensus_data.get('primary_confidence', 0.0):.1%} confidence")
                description_parts.append(f"**Validation Model (GPT-4o Mini):** {consensus_data.get('validation_confidence', 0.0):.1%} confidence")
                description_parts.append(f"**Agreement Score:** {consensus_data.get('agreement_score', 0.0):.1%}")
                description_parts.append("")
            
            # Attachments information
            if attachments:
                description_parts.append("📎 **Attachments**")
                for attachment in attachments:
                    description_parts.append(f"• **{attachment['filename']}** ({attachment['content_type']}, {attachment['file_size']:,} bytes)")
                    if attachment.get('storage_url'):
                        description_parts.append(f"  🔗 [View File]({attachment['storage_url']})")
                description_parts.append("")
            
            # Original email content
            description_parts.append("📝 **Original Email Content**")
            description_parts.append("```")
            description_parts.append(email_data.get('body', 'No email body available'))
            description_parts.append("```")
            description_parts.append("")
            
            # Processing metadata
            description_parts.append("⚙️ **Processing Information**")
            description_parts.append(f"**Workflow ID:** {email_data.get('workflow_id', 'Unknown')}")
            description_parts.append(f"**Processing Time:** {datetime.utcnow().isoformat()}")
            description_parts.append(f"**System:** Zurich AI Claims Processing v1.0")
            
            return "\n".join(description_parts)
            
        except Exception as e:
            dozzle_log("warning", "⚠️ [ZENDESK] Description generation failed, using fallback",
                      error=str(e))
            
            # Fallback description
            return f"""
🤖 AI-Enhanced Claim Processing

📧 Email from: {email_data.get('sender_email', 'Unknown')}
📝 Subject: {email_data.get('subject', 'No Subject')}
🎯 Claim Type: {classification_result.get('final_analysis', {}).get('claim_type', 'Unknown')}

Original Email:
{email_data.get('body', 'No email body available')}

Workflow ID: {email_data.get('workflow_id', 'Unknown')}
"""
    
    def _extract_email_address(self, email_string: str) -> str:
        """
        Extract clean email address from potentially formatted email string

        Args:
            email_string: Email string that might contain display name

        Returns:
            Clean email address only
        """
        import re

        if not email_string:
            raise ValueError("Email address is required - cannot create ticket without sender email")

        # Remove any leading/trailing whitespace
        email_string = email_string.strip()

        # <AUTHOR> <EMAIL>" format
        email_pattern = r'<([^>]+)>'
        match = re.search(email_pattern, email_string)

        if match:
            # Found email in angle brackets
            return match.group(1).strip()

        # Pattern to match standalone email address
        standalone_email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        match = re.search(standalone_email_pattern, email_string)

        if match:
            return match.group(0).strip()

        # If no valid email found, raise error instead of using fallback
        raise ValueError(f"Could not extract valid email address from: {email_string}")

    async def create_simple_ticket(self,
                                 claim_id: str,
                                 workflow_id: str,
                                 email_data: Dict[str, Any],
                                 attachments: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create professional Zendesk ticket with clean formatting for insurance claims

        Args:
            claim_id: UUID of the claim
            workflow_id: Workflow ID from email processing
            email_data: Original email information
            attachments: List of attachment information

        Returns:
            Created ticket information with database sync
        """
        try:
            dozzle_log("info", "🎫 [ZENDESK] Creating professional insurance claim ticket",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      sender=email_data.get('sender_email'))

            # Extract core email information
            original_subject = email_data.get('subject', 'Insurance Claim Submission')
            original_body = email_data.get('body', 'No email content available')
            raw_sender_email = email_data.get('sender_email')
            sender_name = email_data.get('sender_name', email_data.get('from_name', ''))
            received_at = email_data.get('received_at', '')
            
            # Validate required sender email
            if not raw_sender_email:
                raise ValueError("Sender email is required to create ticket")

            # Extract clean email address
            sender_email = self._extract_email_address(raw_sender_email)

            # Generate intelligent claim reference with meaningful prefix
            claim_ref = self._generate_claim_reference(claim_id, email_data)

            # Professional subject with claim reference and key details
            subject = self._generate_professional_subject(claim_ref, original_subject, email_data)

            # Determine appropriate tags based on email content
            tags = self._generate_professional_tags(email_data, attachments)

            # Professional ticket description - PUBLIC COMMENT
            description = self._generate_public_description(
                claim_ref, sender_name, sender_email, received_at, 
                original_subject, original_body, attachments
            )

            # Upload attachments first if present
            uploaded_attachments = []
            attachment_names = []
            if attachments:
                dozzle_log("info", "📎 [ZENDESK] Processing attachments for ticket",
                          attachment_count=len(attachments),
                          claim_id=claim_id)

                for attachment in attachments:
                    filename = attachment.get('filename', 'Unknown file')
                    attachment_names.append(filename)
                    uploaded_token = await self._upload_attachment(attachment)
                    if uploaded_token:
                        uploaded_attachments.append(uploaded_token)
                        dozzle_log("info", "✅ [ZENDESK] Attachment uploaded successfully",
                                  filename=filename,
                                  claim_id=claim_id)
                    else:
                        dozzle_log("error", "❌ [ZENDESK] Failed to upload attachment",
                                  filename=filename,
                                  claim_id=claim_id)

            # Create professional ticket with attachments
            ticket_data = await self._create_ticket_with_retry({
                "subject": subject,
                "description": description,
                "requester_email": sender_email,
                "requester_name": sender_name or "Insurance Claimant",
                "priority": "normal",
                "type": "question",
                "tags": tags,
                "uploads": uploaded_attachments if uploaded_attachments else None
            })

            # Store ticket information in database
            await self._store_ticket_in_database(
                ticket_data=ticket_data,
                workflow_id=workflow_id,
                claim_id=claim_id
            )

            dozzle_log("info", "✅ [ZENDESK] Professional insurance claim ticket created",
                      ticket_id=ticket_data.get('id'),
                      claim_ref=claim_ref,
                      subject=subject,
                      tags=tags,
                      attachments_uploaded=len(uploaded_attachments),
                      attachment_names=attachment_names if attachment_names else "None")

            # 📧 Send professional acknowledgment email immediately after ticket creation
            await self._send_claim_acknowledgment_email(
                claim_id=claim_id,
                claim_ref=claim_ref,
                ticket_id=ticket_data.get('id'),
                customer_email=sender_email,
                customer_name=sender_name,
                email_data=email_data,
                attachments=attachments
            )

            return ticket_data

        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to create professional claim ticket",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      error=str(e))
            raise

    def _generate_professional_tags(self, email_data: Dict[str, Any], attachments: List[Dict[str, Any]] = None) -> List[str]:
        """Generate appropriate tags for insurance claim tickets"""
        tags = ["insurance_claim", "claim_submission"]
        
        # Email content analysis for intelligent tagging
        subject = email_data.get('subject', '').lower()
        body = email_data.get('body', '').lower()
        combined_text = f"{subject} {body}"
        
        # Claim type tags - following insurance industry standards
        if any(keyword in combined_text for keyword in ['injury', 'hurt', 'pain', 'slip', 'fall']):
            tags.append("personal_injury")
        elif any(keyword in combined_text for keyword in ['auto', 'car', 'vehicle', 'accident', 'collision', 'crash']):
            tags.append("auto_claim")
        elif any(keyword in combined_text for keyword in ['home', 'house', 'property', 'fire', 'flood', 'damage', 'roof']):
            tags.append("property_claim")
        elif any(keyword in combined_text for keyword in ['liability', 'legal', 'lawsuit', 'responsible']):
            tags.append("liability_claim")
        else:
            tags.append("general_claim")
        
        # Priority tags
        if any(keyword in combined_text for keyword in ['urgent', 'emergency', 'immediate', 'asap', 'critical']):
            tags.append("high_priority")
        elif any(keyword in combined_text for keyword in ['total loss', 'major damage', 'serious', 'significant']):
            tags.append("major_claim")
        else:
            tags.append("standard_priority")
        
        # Attachment status tags
        if attachments and len(attachments) > 0:
            tags.append("has_attachments")
            if len(attachments) > 2:
                tags.append("multiple_attachments")
        else:
            # Check if attachments were mentioned but not received
            if any(keyword in combined_text for keyword in ['attachment', 'attached', 'document', 'file', 'report', 'form']):
                tags.append("awaiting_attachments")
            else:
                tags.append("no_attachments")
        
        # Source tags
        tags.append("email_submission")
        
        return tags

    def _generate_claim_reference(self, claim_id: str, email_data: Dict[str, Any]) -> str:
        """Generate intelligent claim reference with meaningful prefix"""
        # Define claim type prefixes
        CLAIM_PREFIXES = {
            "personal_injury": "PI",
            "liability": "LI", 
            "auto_claim": "AUTO",
            "auto_accident": "AUTO",
            "property_claim": "PD",
            "property_damage": "PD",
            "medical_claim": "MED",
            "general_claim": "CL",
            "general": "CL"
        }
        
        # Determine claim type from email content
        subject = email_data.get('subject', '').lower()
        body = email_data.get('body', '').lower()
        combined_text = f"{subject} {body}"
        
        # Analyze content for claim type
        prefix = "CL"  # Default prefix
        
        if any(keyword in combined_text for keyword in ['injury', 'hurt', 'pain', 'medical', 'hospital', 'doctor', 'slip', 'fall']):
            prefix = "PI"  # Personal Injury
        elif any(keyword in combined_text for keyword in ['auto', 'car', 'vehicle', 'accident', 'collision', 'crash']):
            prefix = "AUTO"  # Auto Accident
        elif any(keyword in combined_text for keyword in ['property', 'home', 'house', 'fire', 'flood', 'damage', 'roof']):
            prefix = "PD"  # Property Damage
        elif any(keyword in combined_text for keyword in ['liability', 'legal', 'lawsuit', 'responsible']):
            prefix = "LI"  # Liability
        
        # Generate unique suffix from claim_id
        if claim_id:
            unique_suffix = claim_id.replace('-', '').upper()[:8]
        else:
            import uuid
            unique_suffix = str(uuid.uuid4()).replace('-', '').upper()[:8]
        
        return f"{prefix}{unique_suffix}"

    def _generate_professional_subject(self, claim_ref: str, original_subject: str, email_data: Dict[str, Any]) -> str:
        """Generate professional subject with key details"""
        # Extract incident date if available
        body = email_data.get('body', '')
        
        # Try to find date patterns in the email
        import re
        date_patterns = [
            r'(\w+ \d{1,2}, \d{4})',  # "August 27, 2020"
            r'(\d{1,2}/\d{1,2}/\d{4})',  # "08/27/2020"
            r'(\d{4}-\d{2}-\d{2})',  # "2020-08-27"
        ]
        
        incident_date = None
        for pattern in date_patterns:
            match = re.search(pattern, body)
            if match:
                incident_date = match.group(1)
                break
        
        # Clean up subject and add key details
        clean_subject = original_subject.replace('Re:', '').replace('FW:', '').strip()
        
        if incident_date:
            return f"[{claim_ref}] Claim Submission - {clean_subject} ({incident_date})"
        else:
            return f"[{claim_ref}] Claim Submission - {clean_subject}"

    def _advanced_duplicate_removal(self, text: str) -> str:
        """
        Advanced duplicate content removal with intelligent deduplication
        
        Args:
            text: Original email text
            
        Returns:
            Cleaned text with duplicates removed
        """
        try:
            if not text or not text.strip():
                return text
                
            dozzle_log("debug", "🧹 [ZENDESK] Starting advanced duplicate content removal",
                      original_length=len(text),
                      original_lines=len(text.split('\n')))
            
            # Split into paragraphs (by double newlines)
            paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
            
            # Remove duplicate paragraphs while preserving order
            seen_paragraphs = set()
            unique_paragraphs = []
            duplicate_count = 0
            
            for paragraph in paragraphs:
                # Normalize paragraph for comparison (lowercase, remove extra whitespace)
                normalized = ' '.join(paragraph.lower().split())
                
                # Skip very short paragraphs (less than 15 characters) unless they're meaningful
                if len(normalized) < 15 and not any(keyword in normalized for keyword in ['subject:', 'from:', 'to:', 'date:']):
                    continue
                    
                if normalized not in seen_paragraphs:
                    seen_paragraphs.add(normalized)
                    unique_paragraphs.append(paragraph)
                else:
                    duplicate_count += 1
                    
            # Process line-by-line duplicate removal within paragraphs
            final_paragraphs = []
            for paragraph in unique_paragraphs:
                lines = paragraph.split('\n')
                unique_lines = []
                seen_lines = set()
                
                for line in lines:
                    line_normalized = line.strip().lower()
                    
                    # Keep empty lines for formatting
                    if not line_normalized:
                        unique_lines.append(line)
                        continue
                        
                    # Skip very short lines unless they're bullet points or important
                    if len(line_normalized) < 10 and not line_normalized.startswith(('•', '-', '*', '1.', '2.', '3.')):
                        continue
                        
                    if line_normalized not in seen_lines:
                        seen_lines.add(line_normalized)
                        unique_lines.append(line)
                        
                final_paragraphs.append('\n'.join(unique_lines))
                
            # Join back and clean up excessive whitespace
            cleaned_text = '\n\n'.join(final_paragraphs)
            
            # Remove excessive blank lines (more than 2 consecutive)
            import re
            cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text)
            
            # Clean up trailing whitespace
            cleaned_text = '\n'.join(line.rstrip() for line in cleaned_text.split('\n'))
            
            reduction_percent = round((1 - len(cleaned_text)/len(text)) * 100, 1) if len(text) > 0 else 0
            
            dozzle_log("info", "✅ [ZENDESK] Advanced duplicate content removal completed",
                      original_length=len(text),
                      cleaned_length=len(cleaned_text),
                      duplicates_removed=duplicate_count,
                      reduction_percent=reduction_percent,
                      original_paragraphs=len(paragraphs),
                      final_paragraphs=len(final_paragraphs))
            
            return cleaned_text.strip()
            
        except Exception as e:
            dozzle_log("warning", "⚠️ [ZENDESK] Failed to remove duplicate content, using original",
                      error=str(e))
            return text

    def _generate_public_description(self, claim_ref: str, sender_name: str, sender_email: str,
                                   received_at: str, original_subject: str, original_body: str,
                                   attachments: List[Dict[str, Any]]) -> str:
        """Generate professional public description without content duplication"""

        # Extract sender name from email if not provided
        if not sender_name:
            if '<' in sender_email:
                sender_name = sender_email.split('<')[0].strip().strip('"')
            else:
                sender_name = sender_email.split('@')[0].replace('.', ' ').title()

        # 🔧 ENHANCED DUPLICATE CONTENT REMOVAL
        clean_body = self._advanced_duplicate_removal(original_body.strip()) if original_body else ""

        # Simple, clean description without duplication
        description = f"""**Subject:** {original_subject}

**From:** {sender_name} ({sender_email})

**Message:**
{clean_body}"""

        # Handle attachments professionally
        if attachments and len(attachments) > 0:
            description += f"""

**Documents submitted ({len(attachments)} files):**"""
            for attachment in attachments:
                filename = attachment.get('filename', 'Unknown file')
                description += f"\n- {filename}"
        else:
            # Check if attachments were mentioned in email but not received
            if any(keyword in clean_body.lower() for keyword in ['attach', 'document', 'file', 'report', 'form']):
                description += f"""

> **Note:** Attachments were mentioned in the email but were not included."""

        description += f"""

---
**Claim Reference:** {claim_ref.upper()}
**Status:** New claim submission received
**Next Steps:** Awaiting review by claims team

*This ticket was created automatically by the Zurich Claims Processing System*"""

        return description

    async def _upload_attachment(self, attachment: Dict[str, Any]) -> Optional[str]:
        """
        Upload attachment to Zendesk using correct API v2 format with enhanced image support
        
        Args:
            attachment: Dictionary containing filename, content, and content_type
            
        Returns:
            Upload token string if successful, None if failed
        """
        try:
            filename = attachment.get('filename', 'attachment')
            content = attachment.get('content')
            content_type = attachment.get('content_type', 'application/octet-stream')
            
            if not content:
                dozzle_log("warning", "⚠️ [ZENDESK] Attachment has no content, skipping",
                          filename=filename)
                return None
            
            # Enhanced image processing with data URL support
            file_data = await self._process_attachment_content(content, filename, content_type)
            if file_data is None:
                return None
                
            # Detect and update content type for images
            content_type = self._detect_content_type(content, filename, content_type)
            
            dozzle_log("info", "📎 [ZENDESK] Starting attachment upload via API v2",
                      filename=filename,
                      content_type=content_type,
                      size_bytes=len(file_data),
                      is_image=content_type.startswith('image/'))
            
            # Upload to Zendesk using the correct API v2 uploads endpoint
            upload_url = f"{self.base_url}/uploads"
            
            headers = {
                'Content-Type': content_type,
                'Authorization': f'Basic {self.session.headers.get("Authorization", "").replace("Basic ", "")}'
            }
            
            params = {
                'filename': filename
            }
            
            dozzle_log("debug", "📤 [ZENDESK] Making upload request",
                      url=upload_url,
                      filename=filename,
                      content_type=content_type,
                      data_size=len(file_data))
            
            response = self.session.post(
                upload_url,
                data=file_data,
                headers=headers,
                params=params,
                timeout=120  # Longer timeout for file uploads
            )
            
            dozzle_log("debug", "📥 [ZENDESK] Upload response received",
                      status_code=response.status_code,
                      response_size=len(response.text) if response.text else 0)
            
            if response.status_code == 201:
                try:
                    result = response.json()
                    upload_data = result.get('upload', {})
                    upload_token = upload_data.get('token')
                    attachment_data = upload_data.get('attachment', {})
                    upload_url = attachment_data.get('content_url')
                    upload_id = attachment_data.get('id')
                    
                    if upload_token:
                        dozzle_log("info", "✅ [ZENDESK] Attachment uploaded successfully",
                                  filename=filename,
                                  token=upload_token[:20] + "..." if len(upload_token) > 20 else upload_token,
                                  attachment_id=upload_id,
                                  content_url=upload_url[:50] + "..." if upload_url and len(upload_url) > 50 else upload_url)
                        return upload_token
                    else:
                        dozzle_log("error", "❌ [ZENDESK] Upload successful but no token received",
                                  filename=filename,
                                  response_data=result)
                        return None
                        
                except Exception as e:
                    dozzle_log("error", "❌ [ZENDESK] Failed to parse upload response JSON",
                              filename=filename,
                              error=str(e),
                              response_text=response.text[:500])
                    return None
            else:
                error_text = response.text[:500] if response.text else "No response text"
                dozzle_log("error", "❌ [ZENDESK] Failed to upload attachment",
                          filename=filename,
                          status_code=response.status_code,
                          error=error_text,
                          headers=dict(response.headers))
                return None
                
        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Exception during attachment upload",
                      filename=attachment.get('filename', 'unknown'),
                      error=str(e),
                      error_type=type(e).__name__)
            return None

    async def _process_attachment_content(self, content: Any, filename: str, content_type: str) -> Optional[bytes]:
        """
        Process attachment content with enhanced image support and data URL handling
        
        Args:
            content: Raw content (string, bytes, or base64)
            filename: Original filename
            content_type: MIME type
            
        Returns:
            Processed binary data or None if failed
        """
        try:
            if isinstance(content, str):
                # Handle data URLs (e.g., data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...)
                if content.startswith('data:'):
                    dozzle_log("debug", "🖼️ [ZENDESK] Processing data URL content",
                              filename=filename,
                              data_url_prefix=content[:50] + "..." if len(content) > 50 else content)
                    
                    # Extract MIME type from data URL
                    if ',' in content:
                        header, data_part = content.split(',', 1)
                        # Extract MIME type from header (e.g., "data:image/png;base64" -> "image/png")
                        if header.startswith('data:') and ';' in header:
                            mime_type = header.split(';')[0].replace('data:', '')
                            if mime_type and mime_type != content_type:
                                dozzle_log("info", "🔍 [ZENDESK] MIME type detected from data URL",
                                          filename=filename,
                                          detected_type=mime_type,
                                          original_type=content_type)
                        content = data_part  # Use only the base64 part
                    else:
                        dozzle_log("warning", "⚠️ [ZENDESK] Malformed data URL, processing as regular base64",
                                  filename=filename)
                
                # Process as base64 encoded content
                import base64
                try:
                    file_data = base64.b64decode(content, validate=True)
                    dozzle_log("debug", "✅ [ZENDESK] Base64 decode successful",
                              filename=filename,
                              original_size=len(content),
                              decoded_size=len(file_data))
                    return file_data
                except Exception as e:
                    dozzle_log("error", "❌ [ZENDESK] Failed to decode base64 content",
                              filename=filename, 
                              error=str(e),
                              content_preview=content[:100] + "..." if len(content) > 100 else content)
                    return None
                    
            elif isinstance(content, bytes):
                # Already binary content
                dozzle_log("debug", "✅ [ZENDESK] Processing binary content",
                          filename=filename,
                          size=len(content))
                return content
                
            else:
                # Try to convert other types to bytes
                dozzle_log("warning", "⚠️ [ZENDESK] Unknown content type, attempting conversion",
                          filename=filename,
                          content_type=type(content).__name__)
                
                if hasattr(content, 'encode'):
                    return content.encode('utf-8')
                else:
                    return str(content).encode('utf-8')
                    
        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to process attachment content",
                      filename=filename,
                      error=str(e),
                      content_type=type(content).__name__)
            return None

    def _detect_content_type(self, content: Any, filename: str, original_content_type: str) -> str:
        """
        Enhanced MIME type detection for images and other files
        
        Args:
            content: Original content (may contain data URL info)
            filename: Original filename
            original_content_type: Originally provided content type
            
        Returns:
            Detected or improved content type
        """
        try:
            # If content is a data URL, extract MIME type from it
            if isinstance(content, str) and content.startswith('data:'):
                if ',' in content:
                    header = content.split(',')[0]
                    if header.startswith('data:') and ';' in header:
                        mime_type = header.split(';')[0].replace('data:', '')
                        if mime_type and mime_type.startswith(('image/', 'application/', 'text/')):
                            dozzle_log("debug", "🔍 [ZENDESK] MIME type detected from data URL",
                                      filename=filename,
                                      detected_type=mime_type,
                                      original_type=original_content_type)
                            return mime_type
            
            # Enhanced file extension to MIME type mapping
            MIME_TYPE_MAP = {
                # Images
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.bmp': 'image/bmp',
                '.tiff': 'image/tiff',
                '.tif': 'image/tiff',
                '.webp': 'image/webp',
                '.svg': 'image/svg+xml',
                '.ico': 'image/x-icon',
                
                # Documents
                '.pdf': 'application/pdf',
                '.doc': 'application/msword',
                '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                '.xls': 'application/vnd.ms-excel',
                '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                '.ppt': 'application/vnd.ms-powerpoint',
                '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                
                # Text
                '.txt': 'text/plain',
                '.csv': 'text/csv',
                '.json': 'application/json',
                '.xml': 'application/xml',
                
                # Archives
                '.zip': 'application/zip',
                '.rar': 'application/x-rar-compressed',
                '.7z': 'application/x-7z-compressed',
            }
            
            # Check file extension
            if filename and '.' in filename:
                ext = '.' + filename.split('.')[-1].lower()
                if ext in MIME_TYPE_MAP:
                    detected_type = MIME_TYPE_MAP[ext]
                    if detected_type != original_content_type:
                        dozzle_log("debug", "🔍 [ZENDESK] MIME type detected from file extension",
                                  filename=filename,
                                  extension=ext,
                                  detected_type=detected_type,
                                  original_type=original_content_type)
                    return detected_type
            
            # Return original if no better detection
            return original_content_type
            
        except Exception as e:
            dozzle_log("warning", "⚠️ [ZENDESK] MIME type detection failed, using original",
                      filename=filename,
                      error=str(e))
            return original_content_type

    async def _store_ticket_in_database(self, ticket_data: Dict[str, Any], workflow_id: str, claim_id: Optional[str]) -> None:
        """Store ticket information in Supabase database"""
        try:
            if ticket_data and ticket_data.get('id'):
                # Store basic ticket information
                await self.supabase.create_zendesk_ticket_record({
                    'claim_id': claim_id,
                    'zendesk_ticket_id': str(ticket_data['id']),
                    'zendesk_url': f"{self.settings.zendesk_url}/agent/tickets/{ticket_data['id']}",
                    'subject': ticket_data.get('subject', ''),
                    'status': ticket_data.get('status', 'new'),
                    'priority': ticket_data.get('priority', 'normal'),
                    'sync_status': 'synced'
                })

                dozzle_log("info", "✅ [ZENDESK] Ticket stored in database",
                          ticket_id=ticket_data['id'],
                          claim_id=claim_id,
                          workflow_id=workflow_id)
        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to store ticket in database",
                      error=str(e))
            # Don't raise - ticket was created successfully, database storage is secondary

    async def _create_ticket_with_retry(self, ticket_data: Dict[str, Any], max_retries: int = 3) -> Optional[Dict[str, Any]]:
        """
        Create Zendesk ticket with exponential backoff retry logic using direct HTTP API

        Args:
            ticket_data: Ticket information
            max_retries: Maximum number of retry attempts

        Returns:
            Created ticket information dict or None if failed
        """
        for attempt in range(max_retries + 1):
            try:
                dozzle_log("info", f"🎫 [ZENDESK] Creating ticket via direct HTTP API (attempt {attempt + 1})",
                          subject=ticket_data.get('subject', 'No subject'),
                          priority=ticket_data.get('priority', 'normal'),
                          attempt=attempt + 1,
                          max_retries=max_retries)

                # Prepare ticket payload according to Zendesk API v2 format
                payload = {
                    "ticket": {
                        "subject": ticket_data.get('subject', 'Insurance Claim Submission'),
                        "comment": {
                            "body": ticket_data.get('description', 'No description provided'),
                            "public": ticket_data.get('public', True)
                        },
                        "priority": ticket_data.get('priority', 'normal'),
                        "type": ticket_data.get('type', 'question'),
                        "status": ticket_data.get('status', 'new'),
                        "requester": {
                            "email": ticket_data.get('requester_email'),
                            "name": ticket_data.get('requester_name', 'Insurance Claimant')
                        }
                    }
                }

                # Add optional fields if provided
                if ticket_data.get('tags'):
                    payload["ticket"]["tags"] = ticket_data["tags"]

                if ticket_data.get('custom_fields'):
                    payload["ticket"]["custom_fields"] = ticket_data["custom_fields"]
                
                # 🔧 CRITICAL FIX: Add upload tokens to comment for proper attachment handling
                upload_tokens = ticket_data.get('upload_tokens', [])
                if upload_tokens:
                    payload["ticket"]["comment"]["uploads"] = upload_tokens
                    dozzle_log("info", "📎 [ZENDESK] Including upload tokens in ticket creation",
                              token_count=len(upload_tokens),
                              tokens=[token[:20] + "..." if len(token) > 20 else token for token in upload_tokens])

                # Make the API request
                response = self.session.post(
                    f"{self.base_url}/tickets.json",
                    json=payload,
                    timeout=30
                )

                dozzle_log("info", "📡 [ZENDESK] Ticket creation response received",
                          status_code=response.status_code,
                          response_length=len(response.text),
                          attempt=attempt + 1)

                if response.status_code == 201:
                    # Success - ticket created
                    result = response.json()
                    ticket = result.get('ticket', {})

                    ticket_info = {
                        'id': ticket.get('id'),
                        'url': ticket.get('url'),
                        'status': ticket.get('status'),
                        'created_at': ticket.get('created_at'),
                        'subject': ticket.get('subject'),
                        'priority': ticket.get('priority'),
                        'type': ticket.get('type')
                    }

                    dozzle_log("info", "✅ [ZENDESK] Ticket created successfully via direct HTTP API",
                              ticket_id=ticket_info['id'],
                              ticket_url=ticket_info['url'],
                              status=ticket_info['status'],
                              attempt=attempt + 1)

                    return ticket_info

                else:
                    # Error creating ticket
                    error_text = response.text[:500] if response.text else "No response text"
                    raise Exception(f"HTTP {response.status_code}: {error_text}")

            except requests.exceptions.RequestException as e:
                dozzle_log("warning", f"⚠️ [ZENDESK] Network error on attempt {attempt + 1}",
                          error=str(e),
                          error_type=type(e).__name__,
                          attempt=attempt + 1,
                          max_retries=max_retries)

                if attempt < max_retries:
                    # Exponential backoff: 2^attempt seconds
                    wait_time = 2 ** attempt
                    dozzle_log("info", f"🔄 [ZENDESK] Retrying in {wait_time} seconds",
                              wait_time=wait_time,
                              attempt=attempt + 1)
                    await asyncio.sleep(wait_time)
                else:
                    dozzle_log("error", "❌ [ZENDESK] All retry attempts exhausted (network errors)",
                              max_retries=max_retries)
                    raise

            except Exception as e:
                dozzle_log("warning", f"⚠️ [ZENDESK] Unexpected error on attempt {attempt + 1}",
                          error=str(e),
                          error_type=type(e).__name__,
                          attempt=attempt + 1,
                          max_retries=max_retries)

                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    dozzle_log("info", f"🔄 [ZENDESK] Retrying in {wait_time} seconds",
                              wait_time=wait_time,
                              attempt=attempt + 1)
                    await asyncio.sleep(wait_time)
                else:
                    dozzle_log("error", "❌ [ZENDESK] All retry attempts exhausted",
                              max_retries=max_retries)
                    raise

        return None

    async def add_ai_analysis_comment(self,
                                    ticket_id: str,
                                    classification_result: Dict[str, Any],
                                    claim_id: str,
                                    email_data: Dict[str, Any] = None,
                                    attachments: List[Dict[str, Any]] = None) -> bool:
        """
        Add AI analysis as second comment to professional insurance claim ticket

        Args:
            ticket_id: Zendesk ticket ID
            classification_result: AI analysis results
            claim_id: Claim ID for reference
            email_data: Original email information (optional)
            attachments: List of attachments (optional)

        Returns:
            Success status
        """
        try:
            dozzle_log("info", "🤖 [ZENDESK] Adding AI analysis comment to ticket",
                      ticket_id=ticket_id,
                      claim_id=claim_id)

            # Extract AI analysis details with proper structure
            final_analysis = classification_result.get('final_analysis', {})
            extracted_details = final_analysis.get('extracted_details', {})
            consensus_confidence = classification_result.get('consensus_confidence', 0.0)
            
            # Handle missing email_data
            if email_data is None:
                email_data = {'body': '', 'subject': 'Insurance Claim'}

            # Map analysis results to structured format
            email_type = final_analysis.get('email_type', 'CLAIM_SUBMISSION')
            is_claim = email_type == 'CLAIM_SUBMISSION'
            claim_type = final_analysis.get('claim_type', 'GENERAL').upper()
            urgency_level = final_analysis.get('urgency_level', 'MEDIUM').upper()
            confidence = final_analysis.get('confidence_level', 'MEDIUM').upper()
            
            # Extract customer information
            customer_name = extracted_details.get('claimant_name') or email_data.get('sender_name', 'Not provided')
            customer_email = email_data.get('sender_email', 'Not provided')
            customer_phone = extracted_details.get('phone_number') or 'Not provided'
            
            # Extract claim details
            policy_number = extracted_details.get('policy_number') or 'Not provided'
            claim_number = extracted_details.get('claim_number') or 'Not provided'
            incident_date = extracted_details.get('incident_date') or 'Not specified'
            location = extracted_details.get('incident_location') or 'Not specified'
            
            # Generate summary
            summary = f"Submission of documents for a {claim_type.lower().replace('_', '-')} claim"
            if incident_date != 'Not specified':
                summary += f" on {incident_date}"
            if 'medical' in email_data.get('body', '').lower():
                summary += ", seeking medical cost support"
            summary += "."
            
            # Extract key details from email body
            body_text = email_data.get('body', '').lower()
            key_details = []
            
            if incident_date != 'Not specified':
                key_details.append(f"Incident occurred on {incident_date}")
            if location != 'Not specified':
                key_details.append(f"Location: {location}")
            if 'medical' in body_text:
                key_details.append("Seeking support for medical costs")
            if attachments:
                doc_types = [att.get('filename', 'document') for att in attachments]
                key_details.append(f"Documents attached: {', '.join(doc_types)}")
            elif any(word in body_text for word in ['attach', 'document', 'file']):
                key_details.append("Documents mentioned in submission")
            
            # Determine review requirements
            attachments_mentioned = any(word in body_text for word in ['attach', 'document', 'file', 'report', 'form'])
            requires_human_review = consensus_confidence < 0.85 or urgency_level in ['HIGH', 'URGENT']
            requires_immediate_action = urgency_level in ['HIGH', 'URGENT', 'CRITICAL']
            
            # Generate reasoning
            reasoning = f"The email is a formal claim submission regarding {claim_type.lower().replace('_', ' ')}. "
            reasoning += f"It includes specific details about the incident and the nature of the claim. "
            reasoning += f"The urgency is assessed as {urgency_level.lower()} based on the content analysis. "
            if attachments_mentioned:
                reasoning += "The email mentions attachments for supporting documentation. "
            if requires_human_review:
                reasoning += "Human review is recommended due to the complexity or confidence level of the analysis."
            else:
                reasoning += "The analysis confidence is high and the submission appears complete."

            # Build professional AI analysis comment
            ai_comment = f"""AI CLAIM ANALYSIS

Email Type: {email_type}
Is Claim: {'Yes' if is_claim else 'No'}
Claim Type: {claim_type}
Urgency Level: {urgency_level}
Confidence: {confidence}
Policy Number: {policy_number}
Claim Number: {claim_number}
Incident Date: {incident_date}
Location: {location}
Customer Name: {customer_name}
Customer Phone: {customer_phone}
Customer Email: {customer_email}

Summary: {summary}

Key Details:
{chr(10).join(f'- {detail}' for detail in key_details) if key_details else '- Standard claim submission'}

Attachments Mentioned: {'Yes' if attachments_mentioned else 'No'}
Requires Human Review: {'Yes' if requires_human_review else 'No'}
Requires Immediate Action: {'Yes' if requires_immediate_action else 'No'}

Reasoning: {reasoning}"""

            # Add the comment to the ticket
            result = await self.add_comment_to_ticket(
                ticket_id=ticket_id,
                comment=ai_comment,
                public=False  # Internal comment
            )

            dozzle_log("info", "✅ [ZENDESK] AI analysis comment added successfully",
                      ticket_id=ticket_id,
                      claim_id=claim_id,
                      confidence=consensus_confidence)

            return result

        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to add AI analysis comment",
                      ticket_id=ticket_id,
                      claim_id=claim_id,
                      error=str(e))
            return False

    async def add_comment_to_ticket(self,
                                  ticket_id: str,
                                  comment: str,
                                  public: bool = True) -> bool:
        """
        Add a comment to an existing Zendesk ticket using direct HTTP API

        Args:
            ticket_id: Zendesk ticket ID
            comment: Comment text to add
            public: Whether comment is public (default: True) or internal (False)

        Returns:
            True if successful, False otherwise
        """
        try:
            dozzle_log("info", "💬 [ZENDESK] Adding comment to ticket via direct HTTP API",
                      ticket_id=ticket_id,
                      public=public,
                      comment_length=len(comment))

            # Prepare comment payload
            payload = {
                "ticket": {
                    "comment": {
                        "body": comment,
                        "public": public
                    }
                }
            }

            # Make the API request to update the ticket with comment
            response = self.session.put(
                f"{self.base_url}/tickets/{ticket_id}.json",
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                dozzle_log("info", "✅ [ZENDESK] Comment added successfully via direct HTTP API",
                          ticket_id=ticket_id,
                          public=public)
                return True
            else:
                error_text = response.text[:500] if response.text else "No response text"
                dozzle_log("error", "❌ [ZENDESK] Failed to add comment via direct HTTP API",
                          ticket_id=ticket_id,
                          status_code=response.status_code,
                          response_text=error_text)
                return False

        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to add comment to ticket",
                      ticket_id=ticket_id,
                      error=str(e),
                      error_type=type(e).__name__)
            return False

    async def _send_claim_acknowledgment_email(
        self,
        claim_id: str,
        claim_ref: str,
        ticket_id: str,
        customer_email: str,
        customer_name: str,
        email_data: Dict[str, Any],
        attachments: List[Dict[str, Any]] = None
    ) -> bool:
        """
        Send professional acknowledgment email immediately after ticket creation
        
        Args:
            claim_id: Unique claim identifier
            claim_ref: Claim reference number (e.g., PI12345678)
            ticket_id: Zendesk ticket ID
            customer_email: Customer's email address
            customer_name: Customer's name
            email_data: Original email data
            attachments: List of attachments
            
        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            dozzle_log("info", "📧 [ZENDESK] Initiating claim acknowledgment email",
                      claim_id=claim_id,
                      claim_ref=claim_ref,
                      ticket_id=ticket_id,
                      customer_email=customer_email)
            
            # Determine business-friendly claim type
            claim_type = self._determine_business_claim_type(email_data, attachments)
            
            # Extract incident date if available
            incident_date = self._extract_incident_date(email_data)
            
            # Generate tracking URL
            tracking_url = self.email_service.generate_tracking_url(claim_ref)
            
            # Extract threading information from original email for professional reply
            thread_info = self._extract_thread_info_from_email_data(email_data)
            
            dozzle_log("info", "🧵 [EMAIL_THREADING] Extracted thread info for acknowledgment",
                      claim_id=claim_id,
                      has_message_id=bool(thread_info.message_id if thread_info else None),
                      original_subject=thread_info.original_subject if thread_info else None)
            
            # Send acknowledgment email with professional threading
            success = await self.email_service.send_claim_acknowledgment(
                claim_id=claim_ref,  # Use claim reference for display
                customer_email=customer_email,
                customer_name=customer_name or "Valued Customer",
                claim_type=claim_type,
                incident_date=incident_date,
                tracking_url=tracking_url,
                thread_info=thread_info,  # Pass threading info for proper email threading
                zendesk_ticket_id=ticket_id  # Pass ticket ID for automatic comment
            )
            
            if success:
                dozzle_log("info", "✅ [ZENDESK] Claim acknowledgment email sent successfully",
                          claim_id=claim_id,
                          claim_ref=claim_ref,
                          customer_email=customer_email,
                          tracking_url=tracking_url,
                          threading_enabled=bool(thread_info))
                
                # Update claim status in database to reflect acknowledgment sent
                await self._update_claim_status_acknowledged(claim_id, claim_ref)
            else:
                dozzle_log("error", "❌ [ZENDESK] Failed to send claim acknowledgment email",
                          claim_id=claim_id,
                          claim_ref=claim_ref,
                          customer_email=customer_email)
            
            return success
            
        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Error in acknowledgment email process",
                      claim_id=claim_id,
                      customer_email=customer_email,
                      error=str(e))
            return False
    
    def _determine_business_claim_type(self, email_data: Dict[str, Any], attachments: List[Dict[str, Any]] = None) -> str:
        """
        Determine business-friendly claim type from email content
        
        Args:
            email_data: Original email information
            attachments: List of attachments
            
        Returns:
            Business-friendly claim type description
        """
        subject = email_data.get('subject', '').lower()
        body = email_data.get('body', '').lower()
        combined_text = f"{subject} {body}"
        
        # Business terminology mapping
        if any(keyword in combined_text for keyword in ['injury', 'hurt', 'pain', 'medical', 'hospital', 'doctor', 'slip', 'fall']):
            return "Personal Injury Claim"
        elif any(keyword in combined_text for keyword in ['auto', 'car', 'vehicle', 'accident', 'collision', 'crash']):
            return "Auto Insurance Claim"
        elif any(keyword in combined_text for keyword in ['property', 'home', 'house', 'fire', 'flood', 'damage', 'roof']):
            return "Property Damage Claim"
        elif any(keyword in combined_text for keyword in ['liability', 'legal', 'lawsuit', 'responsible']):
            return "Liability Insurance Claim"
        elif any(keyword in combined_text for keyword in ['travel', 'trip', 'vacation', 'flight']):
            return "Travel Insurance Claim"
        else:
            return "General Insurance Claim"
    
    def _extract_incident_date(self, email_data: Dict[str, Any]) -> Optional[str]:
        """
        Extract incident date from email content
        
        Args:
            email_data: Original email information
            
        Returns:
            Incident date if found, None otherwise
        """
        try:
            body = email_data.get('body', '')
            
            # Look for common date patterns in email body
            import re
            
            # Patterns for different date formats
            date_patterns = [
                r'incident.*?(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
                r'occurred.*?(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
                r'date.*?(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
                r'on.*?(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
                r'(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})'
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, body, re.IGNORECASE)
                if match:
                    return match.group(1)
            
            return None
            
        except Exception as e:
            dozzle_log("debug", "Could not extract incident date",
                      error=str(e))
            return None
    
    async def _update_claim_status_acknowledged(self, claim_id: str, claim_ref: str) -> None:
        """
        Update claim status to indicate acknowledgment email was sent
        
        Args:
            claim_id: Claim UUID
            claim_ref: Claim reference number
        """
        try:
            # Update claim status in Supabase
            update_result = await self.supabase.update_claim_status(
                claim_id=claim_id,
                new_status="Claim Received",
                notes=f"Acknowledgment email sent to customer. Claim reference: {claim_ref}"
            )
            
            if update_result:
                dozzle_log("info", "✅ [DATABASE] Claim status updated to acknowledged",
                          claim_id=claim_id,
                          claim_ref=claim_ref,
                          status="Claim Received")
            else:
                dozzle_log("warning", "⚠️ [DATABASE] Could not update claim acknowledgment status",
                          claim_id=claim_id,
                          claim_ref=claim_ref)
                
        except Exception as e:
            dozzle_log("error", "❌ [DATABASE] Error updating claim acknowledgment status",
                      claim_id=claim_id,
                      error=str(e))
    
    def get_business_status(self, technical_status: str) -> str:
        """
        Convert technical status to business-friendly terminology
        
        Args:
            technical_status: Technical status from database
            
        Returns:
            Business-friendly status description
        """
        return self.business_status_map.get(technical_status, technical_status)
    
    def get_business_checkpoint_info(self, current_status: str) -> Dict[str, Any]:
        """
        Get business checkpoint information for current status
        
        Args:
            current_status: Current claim status
            
        Returns:
            Dictionary with checkpoint information
        """
        for checkpoint in self.business_checkpoints:
            if checkpoint['status'] == current_status:
                return checkpoint
        
        # Default if status not found
        return {
            'step': 1,
            'status': current_status,
            'description': 'Your claim is being processed.',
            'business_term': current_status
        }

    def _extract_thread_info_from_email_data(self, email_data: Dict[str, Any]) -> Optional['EmailThreadInfo']:
        """
        Extract threading information from original email data for professional replies
        Enhanced with fallback logic to ensure threading always works
        
        Args:
            email_data: Original email data from the workflow
            
        Returns:
            EmailThreadInfo object with threading details, or None if not available
        """
        try:
            from ..communications.email_service import EmailThreadInfo
            from datetime import datetime
            import hashlib
            
            # Extract Message-ID with multiple fallback strategies
            original_message_id = (
                email_data.get('message_id') or 
                email_data.get('Message-ID') or
                email_data.get('messageId') or
                email_data.get('id')  # Some email parsers use 'id'
            )
            
            # If no Message-ID found, generate a synthetic one for threading
            if not original_message_id:
                sender = email_data.get('sender_email', 'unknown')
                subject = email_data.get('subject', '')
                timestamp = email_data.get('received_at', datetime.datetime.now().isoformat())
                
                # Create deterministic hash for consistency
                unique_data = f"{sender}{subject}{timestamp}"
                hash_id = hashlib.md5(unique_data.encode()).hexdigest()[:12]
                original_message_id = f"<synthetic-{hash_id}@zurich-claims.com>"
                
                dozzle_log("info", "🔧 [THREADING] Generated synthetic Message-ID for threading",
                          synthetic_id=original_message_id,
                          reason="original_missing",
                          sender=sender)
            
            # Ensure Message-ID is properly formatted
            if original_message_id and not (original_message_id.startswith('<') and original_message_id.endswith('>')):
                original_message_id = f"<{original_message_id}>"
            
            # Extract References for threading chain
            references = []
            references_header = email_data.get('references') or email_data.get('References')
            if references_header:
                if isinstance(references_header, str):
                    # Split multiple references and clean them
                    import re
                    refs = re.findall(r'<[^>]+>', references_header)
                    references = [ref.strip() for ref in refs if ref.strip()]
                elif isinstance(references_header, list):
                    references = [ref.strip() for ref in references_header if ref.strip()]
            
            # Add original Message-ID to references chain if not already present
            if original_message_id and original_message_id not in references:
                references.append(original_message_id)
            
            # Extract original email details
            original_subject = email_data.get('subject', '')
            original_sender = email_data.get('sender_name') or email_data.get('from_name', '')
            
            # Parse date with enhanced fallback
            original_date = None
            date_sources = [
                email_data.get('received_at'),
                email_data.get('date'),
                email_data.get('timestamp'),
                email_data.get('Date')
            ]
            
            for date_str in date_sources:
                if date_str:
                    try:
                        if isinstance(date_str, str):
                            # Handle ISO format with timezone
                            if 'T' in date_str:
                                original_date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                            else:
                                # Try parsing common email date formats
                                from email.utils import parsedate_to_datetime
                                original_date = parsedate_to_datetime(date_str)
                            break
                        elif isinstance(date_str, datetime):
                            original_date = date_str
                            break
                    except Exception as date_error:
                        dozzle_log("debug", "Could not parse email date",
                                  date_str=date_str,
                                  error=str(date_error))
                        continue
            
            # Extract body preview for context
            body_preview = ''
            body_sources = [
                email_data.get('body'),
                email_data.get('content'),
                email_data.get('text'),
                email_data.get('plain_text')
            ]
            
            for body in body_sources:
                if body and isinstance(body, str):
                    body_preview = body[:200] + "..." if len(body) > 200 else body
                    break
            
            # Always create thread info if we have essential data
            if original_message_id or original_subject:
                thread_info = EmailThreadInfo(
                    message_id=original_message_id,
                    in_reply_to=None,  # This will be the original Message-ID when we reply
                    references=references,
                    original_subject=original_subject,
                    original_sender=original_sender,
                    original_date=original_date,
                    original_body_preview=body_preview
                )
                
                dozzle_log("info", "🧵 [THREADING] Created enhanced thread info from email data",
                          message_id=original_message_id,
                          original_subject=original_subject,
                          sender=original_sender,
                          references_count=len(references),
                          has_synthetic_id=original_message_id.startswith('<synthetic-') if original_message_id else False)
                
                return thread_info
            else:
                dozzle_log("warning", "⚠️ [THREADING] Insufficient data for threading",
                          has_message_id=bool(original_message_id),
                          has_subject=bool(original_subject))
                return None
                
        except Exception as e:
            dozzle_log("error", "❌ [THREADING] Error extracting thread info from email data",
                      error=str(e),
                      email_data_keys=list(email_data.keys()) if email_data else [])
            return None
