"""
🔧 Zurich AI Claims Processing - Configuration Settings

Centralized configuration management using Pydantic for type safety and validation.
All settings are loaded from environment variables with sensible defaults.
"""

import os
from typing import List, Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # =============================================================================
    # 🤖 AI MODEL CONFIGURATION
    # =============================================================================
    
    # Anthropic Claude (Primary reasoning model) - OPTIONAL
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")

    # OpenAI GPT-4o (Validation & backup model) - REQUIRED
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    
    # Hugging Face (For specialized BERT models)
    huggingface_api_key: Optional[str] = Field(None, env="HUGGINGFACE_API_KEY")
    
    # =============================================================================
    # 🔍 OCR SERVICE CONFIGURATION
    # =============================================================================
    
    # Azure Document Intelligence
    azure_document_intelligence_key: Optional[str] = Field(None, env="AZURE_DOCUMENT_INTELLIGENCE_KEY")
    azure_document_intelligence_endpoint: Optional[str] = Field(None, env="AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")
    
    # Google Cloud Vision API
    google_cloud_vision_api_key: Optional[str] = Field(None, env="GOOGLE_CLOUD_VISION_API_KEY")
    google_application_credentials: Optional[str] = Field(None, env="GOOGLE_APPLICATION_CREDENTIALS")
    
    # AWS Textract
    aws_access_key_id: Optional[str] = Field(None, env="AWS_ACCESS_KEY_ID")
    aws_secret_access_key: Optional[str] = Field(None, env="AWS_SECRET_ACCESS_KEY")
    aws_region: str = Field("us-east-1", env="AWS_REGION")
    
    # =============================================================================
    # 📧 EMAIL CONFIGURATION
    # =============================================================================
    
    # Gmail Configuration - OPTIONAL (for additional email services)
    gmail_email: Optional[str] = Field(None, env="GMAIL_EMAIL")
    gmail_app_password: Optional[str] = Field(None, env="GMAIL_APP_PASSWORD")
    
    # IMAP Configuration for EmailMonitor - MUST BE SET VIA ENVIRONMENT VARIABLES
    claims_email: str = Field(..., env="EMAIL")
    claims_email_password: str = Field(..., env="CLAIMS_EMAIL_PASSWORD")
    imap_server: str = Field("imap.gmail.com", env="IMAP_SERVER")
    imap_port: int = Field(993, env="IMAP_PORT")
    
    # SMTP Configuration
    smtp_server: str = Field("smtp.gmail.com", env="SMTP_SERVER")
    smtp_port: int = Field(587, env="SMTP_PORT")
    smtp_username: Optional[str] = Field(None, env="SMTP_USERNAME")
    smtp_password: Optional[str] = Field(None, env="SMTP_PASSWORD")
    
    # =============================================================================
    # 🎫 ZENDESK INTEGRATION
    # =============================================================================

    # Zendesk API - REQUIRED
    zendesk_subdomain: str = Field(..., env="ZENDESK_SUBDOMAIN")
    zendesk_email: str = Field(..., env="ZENDESK_EMAIL")
    zendesk_token: str = Field(..., env="ZENDESK_API_TOKEN")

    @property
    def zendesk_url(self) -> str:
        """Construct full Zendesk URL from subdomain"""
        return f"https://{self.zendesk_subdomain}.zendesk.com"
    
    # =============================================================================
    # 🏛️ POLICY API CONFIGURATION
    # =============================================================================
    
    # Insurance Policy API
    policy_api_url: Optional[str] = Field(None, env="POLICY_API_URL")
    policy_api_key: Optional[str] = Field(None, env="POLICY_API_KEY")
    
    # =============================================================================
    # 🗄️ DATABASE CONFIGURATION
    # =============================================================================

    # PostgreSQL Database
    database_url: str = Field("********************************************/zurich_claims", env="DATABASE_URL")
    database_host: str = Field("localhost", env="DATABASE_HOST")
    database_port: int = Field(5432, env="DATABASE_PORT")
    database_name: str = Field("zurich_claims", env="DATABASE_NAME")
    database_user: str = Field("postgres", env="DATABASE_USER")
    database_password: str = Field("password", env="DATABASE_PASSWORD")

    # Supabase Configuration - REQUIRED
    supabase_url: str = Field(..., env="SUPABASE_URL")
    supabase_anon_key: str = Field(..., env="SUPABASE_ANON_KEY")
    supabase_service_role_key: str = Field(..., env="SUPABASE_SERVICE_ROLE_KEY")
    
    # Redis
    redis_url: str = Field("redis://localhost:6379/0", env="REDIS_URL")
    redis_host: str = Field("localhost", env="REDIS_HOST")
    redis_port: int = Field(6379, env="REDIS_PORT")
    redis_db: int = Field(0, env="REDIS_DB")
    
    # =============================================================================
    # 🌐 WEB APPLICATION CONFIGURATION
    # =============================================================================
    
    # FastAPI Application
    app_host: str = Field("0.0.0.0", env="APP_HOST")
    app_port: int = Field(8000, env="APP_PORT")
    app_debug: bool = Field(False, env="APP_DEBUG")
    app_secret_key: str = Field("dev-secret-key-change-in-production", env="APP_SECRET_KEY")

    # Webhook Configuration
    webhook_base_url: str = Field("http://localhost:8000", env="WEBHOOK_BASE_URL")
    webhook_secret: str = Field("dev-webhook-secret", env="WEBHOOK_SECRET")
    
    # Frontend/Tracking Configuration
    tracking_base_url: str = Field("http://localhost:3000", env="TRACKING_BASE_URL")
    frontend_url: str = Field("http://localhost:3000", env="FRONTEND_URL")
    
    # =============================================================================
    # 📊 MONITORING & LOGGING
    # =============================================================================
    
    # Sentry (Error tracking)
    sentry_dsn: Optional[str] = Field(None, env="SENTRY_DSN")
    
    # Prometheus (Metrics)
    prometheus_port: int = Field(9090, env="PROMETHEUS_PORT")
    
    # Log Level
    log_level: str = Field("INFO", env="LOG_LEVEL")
    
    # =============================================================================
    # 🔒 SECURITY CONFIGURATION
    # =============================================================================
    
    # Encryption keys
    encryption_key: str = Field("dev-encryption-key-32-chars-long!", env="ENCRYPTION_KEY")
    jwt_secret_key: str = Field("dev-jwt-secret-key-change-in-production", env="JWT_SECRET_KEY")
    
    # CORS Configuration
    allowed_origins: List[str] = Field(
        ["http://localhost:3000"], 
        env="ALLOWED_ORIGINS"
    )
    
    # =============================================================================
    # 🇨🇦 CANADIAN LEGAL FRAMEWORK
    # =============================================================================
    
    # Canadian Legal Database API
    canadian_legal_api_key: Optional[str] = Field(None, env="CANADIAN_LEGAL_API_KEY")
    canadian_legal_api_url: Optional[str] = Field(None, env="CANADIAN_LEGAL_API_URL")
    
    # Provincial Law Database
    provincial_law_db_url: Optional[str] = Field(None, env="PROVINCIAL_LAW_DB_URL")
    
    # =============================================================================
    # 🎯 WORKFLOW CONFIGURATION
    # =============================================================================
    
    # Processing timeouts (in minutes)
    email_processing_timeout: int = Field(30, env="EMAIL_PROCESSING_TIMEOUT")
    ocr_processing_timeout: int = Field(120, env="OCR_PROCESSING_TIMEOUT")
    human_approval_timeout: int = Field(240, env="HUMAN_APPROVAL_TIMEOUT")
    
    # Claim value thresholds
    high_value_claim_threshold: float = Field(100000.0, env="HIGH_VALUE_CLAIM_THRESHOLD")
    critical_claim_threshold: float = Field(500000.0, env="CRITICAL_CLAIM_THRESHOLD")
    
    # =============================================================================
    # 📈 PERFORMANCE CONFIGURATION
    # =============================================================================
    
    # Concurrency limits
    max_concurrent_ocr_jobs: int = Field(5, env="MAX_CONCURRENT_OCR_JOBS")
    max_concurrent_ai_analysis: int = Field(3, env="MAX_CONCURRENT_AI_ANALYSIS")
    max_email_batch_size: int = Field(10, env="MAX_EMAIL_BATCH_SIZE")
    
    # Cache TTL (in seconds)
    cache_ttl_short: int = Field(300, env="CACHE_TTL_SHORT")      # 5 minutes
    cache_ttl_medium: int = Field(3600, env="CACHE_TTL_MEDIUM")   # 1 hour
    cache_ttl_long: int = Field(86400, env="CACHE_TTL_LONG")      # 24 hours
    
    # =============================================================================
    # VALIDATORS
    # =============================================================================
    
    @field_validator("allowed_origins", mode="before")
    @classmethod
    def parse_allowed_origins(cls, v):
        """Parse comma-separated origins into list"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()

    @field_validator("email_processing_timeout", "ocr_processing_timeout", "human_approval_timeout")
    @classmethod
    def validate_positive_timeout(cls, v):
        """Ensure timeouts are positive"""
        if v <= 0:
            raise ValueError("Timeout must be positive")
        return v

    @field_validator("high_value_claim_threshold", "critical_claim_threshold")
    @classmethod
    def validate_positive_threshold(cls, v):
        """Ensure thresholds are positive"""
        if v <= 0:
            raise ValueError("Claim threshold must be positive")
        return v
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore",  # Ignore extra fields to prevent validation errors
        "env_prefix": "",  # No prefix for environment variables
        "validate_assignment": True
    }

    def __init__(self, **kwargs):
        """Initialize settings with explicit environment variable handling"""
        # Force environment variable reading for critical fields
        import os
        if not kwargs.get('zendesk_token') and os.getenv('ZENDESK_API_TOKEN'):
            kwargs['zendesk_token'] = os.getenv('ZENDESK_API_TOKEN')
        if not kwargs.get('claims_email') and os.getenv('EMAIL'):
            kwargs['claims_email'] = os.getenv('EMAIL')
        if not kwargs.get('claims_email_password') and os.getenv('CLAIMS_EMAIL_PASSWORD'):
            kwargs['claims_email_password'] = os.getenv('CLAIMS_EMAIL_PASSWORD')
        super().__init__(**kwargs)
        
    def __repr__(self) -> str:
        """String representation (without sensitive data)"""
        return f"Settings(app_host={self.app_host}, app_port={self.app_port}, log_level={self.log_level})"
