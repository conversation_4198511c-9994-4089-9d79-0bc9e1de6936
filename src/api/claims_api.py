"""
Claims Tracking API for Zurich Claims Processing

Provides REST endpoints for claim tracking with business-friendly terminology
and real-time status updates from the database.
"""

import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from flask import Flask, jsonify, request
from flask_cors import CORS
import structlog

from ..config.settings import Settings
from ..database.supabase_client import SupabaseClient
from ..zendesk_integration.zendesk_client import ZendeskClient
from ..utils.dozzle_logger import dozzle_log

logger = structlog.get_logger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend


class ClaimsTrackingAPI:
    """
    API service for claim tracking and status management
    
    Features:
    - Business-friendly status conversion
    - Real-time database integration
    - Comprehensive claim details
    - Document tracking
    - Timeline generation
    """
    
    def __init__(self, settings: Settings, supabase_client: SupabaseClient, zendesk_client: ZendeskClient):
        self.settings = settings
        self.supabase = supabase_client
        self.zendesk = zendesk_client
        
        # Initialize Flask routes
        self._setup_routes()
    
    def _setup_routes(self):
        """Setup Flask API routes"""
        
        @app.route('/api/claims/track/<claim_reference>', methods=['GET'])
        async def track_claim(claim_reference: str):
            """
            Track claim by reference number
            
            Args:
                claim_reference: Claim reference number (e.g., **********)
                
            Returns:
                JSON response with claim details and business status
            """
            try:
                dozzle_log("info", "📡 [API] Claim tracking request received",
                          claim_reference=claim_reference,
                          request_ip=request.remote_addr)
                
                # Validate claim reference format
                if not claim_reference or len(claim_reference) < 6:
                    return jsonify({
                        'error': 'Invalid claim reference format',
                        'message': 'Please provide a valid claim reference number'
                    }), 400
                
                # Fetch claim data from database
                claim_data = await self._get_claim_by_reference(claim_reference)
                
                if not claim_data:
                    dozzle_log("warning", "🔍 [API] Claim not found",
                              claim_reference=claim_reference)
                    return jsonify({
                        'error': 'Claim not found',
                        'message': 'No claim found with the provided reference number'
                    }), 404
                
                # Convert to business-friendly format
                tracking_data = await self._format_tracking_response(claim_data)
                
                dozzle_log("info", "✅ [API] Claim tracking data retrieved successfully",
                          claim_reference=claim_reference,
                          status=tracking_data.get('business_status'))
                
                return jsonify(tracking_data), 200
                
            except Exception as e:
                dozzle_log("error", "❌ [API] Error retrieving claim tracking data",
                          claim_reference=claim_reference,
                          error=str(e))
                return jsonify({
                    'error': 'Internal server error',
                    'message': 'Unable to retrieve claim information at this time'
                }), 500
        
        @app.route('/api/claims/status-update', methods=['POST'])
        async def update_claim_status():
            """
            Update claim status (for internal use)
            
            Request body:
                {
                    "claim_id": "uuid",
                    "new_status": "status",
                    "notes": "optional notes"
                }
            """
            try:
                data = request.get_json()
                claim_id = data.get('claim_id')
                new_status = data.get('new_status')
                notes = data.get('notes', '')
                
                if not claim_id or not new_status:
                    return jsonify({
                        'error': 'Missing required fields',
                        'message': 'claim_id and new_status are required'
                    }), 400
                
                # Update status in database
                success = await self.supabase.update_claim_status(
                    claim_id=claim_id,
                    new_status=new_status,
                    notes=notes
                )
                
                if success:
                    return jsonify({
                        'success': True,
                        'message': 'Claim status updated successfully'
                    }), 200
                else:
                    return jsonify({
                        'error': 'Update failed',
                        'message': 'Unable to update claim status'
                    }), 400
                
            except Exception as e:
                dozzle_log("error", "❌ [API] Error updating claim status", error=str(e))
                return jsonify({
                    'error': 'Internal server error',
                    'message': 'Unable to update claim status'
                }), 500
        
        @app.route('/api/health', methods=['GET'])
        def health_check():
            """Health check endpoint"""
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.datetime.now().isoformat(),
                'service': 'Claims Tracking API'
            }), 200
    
    async def _get_claim_by_reference(self, claim_reference: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve claim data by reference number
        
        Args:
            claim_reference: Claim reference number
            
        Returns:
            Claim data dictionary or None if not found
        """
        try:
            # Search by claim reference in the database
            # This assumes the claim reference is stored in a field or can be derived from claim_id
            
            # Option 1: If claim_reference is stored directly
            query_result = await self.supabase.get_claim_by_reference(claim_reference)
            
            if query_result:
                return query_result
            
            # Option 2: If claim_reference needs to be derived from claim_id
            # Search for claim where claim_id contains the reference suffix
            reference_suffix = claim_reference[2:] if len(claim_reference) > 2 else claim_reference
            all_claims = await self.supabase.get_all_claims()
            
            for claim in all_claims:
                claim_id = claim.get('id', '')
                if reference_suffix.lower() in claim_id.replace('-', '').lower():
                    return claim
            
            return None
            
        except Exception as e:
            dozzle_log("error", "❌ [DATABASE] Error retrieving claim by reference",
                      claim_reference=claim_reference,
                      error=str(e))
            return None
    
    async def _format_tracking_response(self, claim_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format claim data for tracking response with business terminology
        
        Args:
            claim_data: Raw claim data from database
            
        Returns:
            Formatted tracking response
        """
        try:
            # Extract basic claim information
            claim_id = claim_data.get('id', '')
            current_status = claim_data.get('workflow_status', 'NEW')
            
            # Generate claim reference if not stored
            claim_reference = claim_data.get('claim_reference')
            if not claim_reference:
                claim_reference = self._generate_claim_reference_from_id(claim_id, claim_data)
            
            # Convert to business status
            business_status = self.zendesk.get_business_status(current_status)
            
            # Get checkpoint information
            checkpoint_info = self.zendesk.get_business_checkpoint_info(business_status)
            current_step = checkpoint_info.get('step', 1)
            
            # Extract customer information from email data
            email_content = claim_data.get('email_content', {})
            if isinstance(email_content, str):
                import json
                try:
                    email_content = json.loads(email_content)
                except:
                    email_content = {}
            
            # Format customer name
            customer_name = (
                email_content.get('sender_name') or 
                email_content.get('from_name') or 
                'Valued Customer'
            )
            
            # Determine claim type
            claim_type = self._determine_claim_type_from_data(claim_data)
            
            # Extract dates
            submission_date = claim_data.get('created_at', datetime.datetime.now().isoformat())
            incident_date = self._extract_incident_date_from_data(claim_data)
            last_updated = claim_data.get('updated_at', submission_date)
            
            # Get attachments information
            documents = await self._get_claim_documents(claim_id)
            
            # Build response
            tracking_response = {
                'claim_id': claim_id,
                'claim_reference': claim_reference,
                'status': current_status,
                'business_status': business_status,
                'customer_name': customer_name,
                'claim_type': claim_type,
                'incident_date': incident_date or 'Not specified',
                'submission_date': submission_date,
                'assigned_executive': self._get_assigned_executive(claim_data),
                'estimated_completion': self._calculate_estimated_completion(current_step),
                'current_step': current_step,
                'total_steps': 5,
                'description': checkpoint_info.get('description', 'Your claim is being processed.'),
                'last_updated': last_updated,
                'documents': documents,
                'timeline': self._generate_timeline(claim_data, current_step)
            }
            
            return tracking_response
            
        except Exception as e:
            dozzle_log("error", "❌ [API] Error formatting tracking response",
                      claim_id=claim_data.get('id', 'unknown'),
                      error=str(e))
            raise
    
    def _generate_claim_reference_from_id(self, claim_id: str, claim_data: Dict[str, Any]) -> str:
        """Generate claim reference from claim ID and data"""
        try:
            # Extract classification to determine prefix
            classification = claim_data.get('classification_result', {})
            if isinstance(classification, str):
                import json
                try:
                    classification = json.loads(classification)
                except:
                    classification = {}
            
            claim_type = classification.get('final_analysis', {}).get('claim_type', 'general')
            
            # Determine prefix
            prefix = 'CL'  # Default
            if 'injury' in claim_type.lower() or 'personal' in claim_type.lower():
                prefix = 'PI'
            elif 'auto' in claim_type.lower() or 'vehicle' in claim_type.lower():
                prefix = 'AUTO'
            elif 'property' in claim_type.lower():
                prefix = 'PD'
            elif 'liability' in claim_type.lower():
                prefix = 'LI'
            
            # Use first 8 characters of claim ID (without hyphens) as suffix
            suffix = claim_id.replace('-', '').upper()[:8]
            
            return f"{prefix}{suffix}"
            
        except Exception as e:
            # Fallback to simple reference
            suffix = claim_id.replace('-', '').upper()[:8] if claim_id else 'UNKNOWN'
            return f"CL{suffix}"
    
    def _determine_claim_type_from_data(self, claim_data: Dict[str, Any]) -> str:
        """Determine business-friendly claim type from claim data"""
        try:
            classification = claim_data.get('classification_result', {})
            if isinstance(classification, str):
                import json
                try:
                    classification = json.loads(classification)
                except:
                    classification = {}
            
            claim_type = classification.get('final_analysis', {}).get('claim_type', 'general')
            
            # Convert to business-friendly terms
            type_mapping = {
                'personal_injury': 'Personal Injury Claim',
                'auto_claim': 'Auto Insurance Claim',
                'property_claim': 'Property Damage Claim',
                'liability_claim': 'Liability Insurance Claim',
                'general': 'General Insurance Claim'
            }
            
            return type_mapping.get(claim_type.lower(), 'General Insurance Claim')
            
        except Exception as e:
            return 'General Insurance Claim'
    
    def _extract_incident_date_from_data(self, claim_data: Dict[str, Any]) -> Optional[str]:
        """Extract incident date from claim data"""
        try:
            classification = claim_data.get('classification_result', {})
            if isinstance(classification, str):
                import json
                try:
                    classification = json.loads(classification)
                except:
                    classification = {}
            
            extracted_details = classification.get('final_analysis', {}).get('extracted_details', {})
            return extracted_details.get('incident_date')
            
        except Exception as e:
            return None
    
    def _get_assigned_executive(self, claim_data: Dict[str, Any]) -> str:
        """Get assigned executive (placeholder implementation)"""
        # Placeholder - in production this would come from the database
        return "Being Assigned"
    
    def _calculate_estimated_completion(self, current_step: int) -> str:
        """Calculate estimated completion date based on current step"""
        try:
            # Business days estimation per step
            days_per_step = {
                1: 1,   # Claim Received
                2: 3,   # Document Validation  
                3: 7,   # Under Investigation
                4: 5,   # Executive Review
                5: 0    # Completed
            }
            
            remaining_days = sum(days_per_step[step] for step in range(current_step + 1, 6))
            estimated_date = datetime.datetime.now() + timedelta(days=remaining_days)
            
            return estimated_date.strftime("%B %d, %Y")
            
        except Exception as e:
            return "To be determined"
    
    async def _get_claim_documents(self, claim_id: str) -> List[Dict[str, Any]]:
        """Get documents associated with the claim"""
        try:
            attachments = await self.supabase.get_claim_attachments(claim_id)
            
            documents = []
            for attachment in attachments:
                documents.append({
                    'id': attachment.get('id', ''),
                    'name': attachment.get('filename', 'Document'),
                    'type': attachment.get('content_type', 'Unknown'),
                    'upload_date': attachment.get('created_at', ''),
                    'status': 'verified'  # Placeholder - could be enhanced
                })
            
            return documents
            
        except Exception as e:
            dozzle_log("debug", "Could not retrieve claim documents",
                      claim_id=claim_id,
                      error=str(e))
            return []
    
    def _generate_timeline(self, claim_data: Dict[str, Any], current_step: int) -> List[Dict[str, Any]]:
        """Generate timeline events for the claim"""
        try:
            timeline = []
            
            # Basic timeline based on current step
            checkpoints = [
                ('Claim Received', 'Your claim submission was received and logged in our system.'),
                ('Document Validation', 'All submitted documents are being verified for completeness.'),
                ('Under Investigation', 'Our claims team is conducting a thorough assessment.'),
                ('Executive Review', 'A senior executive is reviewing your claim for final approval.'),
                ('Claim Processed', 'Your claim has been finalized and settlement details are being prepared.')
            ]
            
            submission_date = claim_data.get('created_at', datetime.datetime.now().isoformat())
            base_date = datetime.fromisoformat(submission_date.replace('Z', '+00:00'))
            
            for i, (status, description) in enumerate(checkpoints, 1):
                event_date = base_date + timedelta(days=(i-1) * 2)
                timeline.append({
                    'date': event_date.isoformat(),
                    'status': status,
                    'description': description,
                    'completed': i <= current_step
                })
            
            return timeline
            
        except Exception as e:
            return []


def create_claims_api(settings: Settings, supabase_client: SupabaseClient, zendesk_client: ZendeskClient) -> Flask:
    """
    Create and configure the Claims API Flask application
    
    Args:
        settings: Application settings
        supabase_client: Database client
        zendesk_client: Zendesk integration client
        
    Returns:
        Configured Flask application
    """
    api = ClaimsTrackingAPI(settings, supabase_client, zendesk_client)
    return app


if __name__ == '__main__':
    # Development server
    app.run(debug=True, host='0.0.0.0', port=5000) 