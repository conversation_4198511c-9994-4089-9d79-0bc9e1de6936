"""
OCR Processing Service for Zurich Claims Processing

Integrates with Zurich OCR API to process documents and extract text content.
Handles file retrieval from Supabase storage and stores OCR results back to database.
"""

import asyncio
import aiohttp
import aiofiles
import os
import tempfile
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import structlog
from pathlib import Path

from ..config.settings import Settings
from ..database.supabase_client import SupabaseClient
from ..utils.dozzle_logger import dozzle_log

logger = structlog.get_logger(__name__)


class ZurichOCRService:
    """
    OCR Processing Service using Zurich OCR API
    
    Features:
    - Downloads files from Supabase storage
    - Processes via Zurich OCR API
    - Stores OCR results back to database
    - Supports batch processing
    - Comprehensive error handling and retry logic
    """
    
    def __init__(self, settings: Settings, supabase_client: SupabaseClient, zendesk_client=None):
        self.settings = settings
        self.supabase = supabase_client
        self.zendesk = zendesk_client  # Add ZendeskClient for comments
        self.ocr_api_url = "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/batch-process"
        self.max_retries = 3
        self.timeout = 300  # 5 minutes timeout
        
        # OCR configuration matching the provided API format
        self.ocr_config = {
            "ocr_engine": "google",
            "google_processor": "OCR_PROCESSOR", 
            "llm_routing_enabled": False,
            "post_processing": "v1",
            "preprocessing": "none",
            "parallel_processing": False
        }
        
        dozzle_log("info", "🔍 [OCR_SERVICE] Zurich OCR Service initialized",
                  api_url=self.ocr_api_url,
                  timeout=self.timeout,
                  max_retries=self.max_retries,
                  zendesk_enabled=bool(zendesk_client))
    
    async def process_claim_attachments(self, 
                                      claim_id: str, 
                                      zendesk_ticket_id: str,
                                      workflow_id: str) -> Dict[str, Any]:
        """
        Process all attachments for a claim through OCR
        
        Args:
            claim_id: UUID of the claim
            zendesk_ticket_id: Zendesk ticket ID for comments
            workflow_id: Workflow ID for tracking
            
        Returns:
            Dictionary with OCR processing results
        """
        try:
            dozzle_log("info", "🔍 [OCR_SERVICE] Starting OCR processing for claim",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      zendesk_ticket_id=zendesk_ticket_id)
            
            # 1. Add comment to Zendesk that document processing started
            await self._add_processing_started_comment(zendesk_ticket_id)
            
            # 2. Get attachments from database
            attachments = await self.supabase.get_claim_attachments(claim_id)
            
            if not attachments:
                dozzle_log("info", "📄 [OCR_SERVICE] No attachments found for processing",
                          claim_id=claim_id)
                return {
                    "status": "no_attachments",
                    "processed_count": 0,
                    "total_count": 0,
                    "attachments": []
                }
            
            dozzle_log("info", "📄 [OCR_SERVICE] Found attachments for processing",
                      claim_id=claim_id,
                      attachment_count=len(attachments))
            
            # 3. Process attachments through OCR
            processing_results = await self._process_attachments_batch(attachments, claim_id)
            
            # 4. Update database with OCR results
            await self._update_attachments_with_ocr_results(processing_results, claim_id)
            
            # 5. Add completion comment to Zendesk
            await self._add_processing_completed_comment(zendesk_ticket_id, processing_results)
            
            # 6. Log comprehensive results
            await self._log_comprehensive_results(claim_id, workflow_id, processing_results)
            
            return {
                "status": "completed",
                "processed_count": len([r for r in processing_results if r["status"] == "success"]),
                "total_count": len(processing_results),
                "attachments": processing_results
            }
            
        except Exception as e:
            dozzle_log("error", "❌ [OCR_SERVICE] OCR processing failed",
                      claim_id=claim_id,
                      error=str(e),
                      error_type=type(e).__name__)
            raise

    async def process_claim_temp_files(self,
                                     claim_id: str,
                                     temp_file_paths: List[str],
                                     zendesk_ticket_id: str = None) -> Dict[str, Any]:
        """
        Process organized temp files directly through OCR (bypasses Supabase download)

        This method processes files from tmp/claimid/files/ structure using form data
        for the OCR API, which should eliminate any email header contamination.

        Args:
            claim_id: Claim ID for tracking
            temp_file_paths: List of file paths in tmp/claimid/files/
            zendesk_ticket_id: Optional Zendesk ticket ID for comments

        Returns:
            Dict with processing results and summary
        """
        dozzle_log("info", "🚀 [OCR_TEMP_FILES] Starting OCR processing of organized temp files",
                  claim_id=claim_id,
                  file_count=len(temp_file_paths))

        try:
            # Add Zendesk comment if enabled
            if self.zendesk and zendesk_ticket_id:
                await self._add_processing_started_comment(zendesk_ticket_id)

            # Process temp files directly using form data
            processing_results = await self._process_temp_files_batch(temp_file_paths, claim_id)

            # Calculate summary statistics
            successful_results = [r for r in processing_results if r["status"] == "success"]
            failed_results = [r for r in processing_results if r["status"] != "success"]

            summary = {
                "claim_id": claim_id,
                "total_files": len(temp_file_paths),
                "successful": len(successful_results),
                "failed": len(failed_results),
                "processing_results": processing_results,
                "processed_at": datetime.utcnow().isoformat()
            }

            # Add completion comment to Zendesk
            if self.zendesk and zendesk_ticket_id:
                await self._add_processing_completed_comment(zendesk_ticket_id, processing_results)

            dozzle_log("info", "✅ [OCR_TEMP_FILES] Completed OCR processing of temp files",
                      claim_id=claim_id,
                      total_files=len(temp_file_paths),
                      successful=len(successful_results),
                      failed=len(failed_results))

            return summary

        except Exception as e:
            dozzle_log("error", "❌ [OCR_TEMP_FILES] Failed to process temp files",
                      claim_id=claim_id,
                      error=str(e),
                      error_type=type(e).__name__)
            raise
    
    async def _add_processing_started_comment(self, zendesk_ticket_id: str) -> None:
        """Add comment to Zendesk that document processing has started"""
        try:
            comment = """DOCUMENT PROCESSING STARTED

The system has begun processing uploaded documents through OCR (Optical Character Recognition).

Processing Steps:
1. Documents retrieved from secure storage
2. OCR text extraction in progress
3. Analysis and classification pending
4. Results will be updated shortly

Estimated processing time: 2-5 minutes depending on document complexity and count.

You will be notified when processing is complete."""

            dozzle_log("info", "💬 [OCR_SERVICE] Adding document processing started comment",
                      zendesk_ticket_id=zendesk_ticket_id)
            
            # Add comment to Zendesk ticket if client is available
            if self.zendesk:
                comment_success = await self.zendesk.add_comment_to_ticket(
                    ticket_id=zendesk_ticket_id,
                    comment=comment,
                    public=False  # Internal comment for agent use
                )
                
                if comment_success:
                    dozzle_log("info", "✅ [OCR_SERVICE] Processing started comment added to Zendesk",
                              zendesk_ticket_id=zendesk_ticket_id,
                              comment_added=True)
                else:
                    dozzle_log("warning", "⚠️ [OCR_SERVICE] Failed to add processing started comment",
                              zendesk_ticket_id=zendesk_ticket_id,
                              comment_added=False)
            else:
                dozzle_log("warning", "⚠️ [OCR_SERVICE] No Zendesk client available for comment",
                          zendesk_ticket_id=zendesk_ticket_id)
            
        except Exception as e:
            dozzle_log("error", "❌ [OCR_SERVICE] Failed to add processing started comment",
                      zendesk_ticket_id=zendesk_ticket_id,
                      error=str(e),
                      error_type=type(e).__name__)
            # Don't raise - this is not critical

    async def _process_temp_files_batch(self,
                                      temp_file_paths: List[str],
                                      claim_id: str) -> List[Dict[str, Any]]:
        """
        Process temp files directly using form data for OCR API

        This bypasses Supabase download and uses clean files from tmp/claimid/files/
        """
        processing_results = []

        if not temp_file_paths:
            dozzle_log("warning", "⚠️ [OCR_TEMP_BATCH] No temp files to process",
                      claim_id=claim_id)
            return processing_results

        # Prepare files for form data submission
        files_for_processing = []
        for file_path in temp_file_paths:
            try:
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    filename = os.path.basename(file_path)
                    file_size = os.path.getsize(file_path)

                    files_for_processing.append({
                        "file_path": file_path,
                        "filename": filename,
                        "file_size": file_size
                    })

                    dozzle_log("info", "📁 [OCR_TEMP_BATCH] Added file for processing",
                              filename=filename,
                              file_size=file_size,
                              file_path=file_path)
                else:
                    dozzle_log("warning", "⚠️ [OCR_TEMP_BATCH] File not found or not accessible",
                              file_path=file_path)
                    processing_results.append({
                        "filename": os.path.basename(file_path),
                        "status": "file_not_found",
                        "error": f"File not found: {file_path}",
                        "ocr_text": None,
                        "ocr_confidence": None
                    })

            except Exception as e:
                dozzle_log("error", "❌ [OCR_TEMP_BATCH] Error preparing file",
                          file_path=file_path,
                          error=str(e))
                processing_results.append({
                    "filename": os.path.basename(file_path),
                    "status": "preparation_failed",
                    "error": str(e),
                    "ocr_text": None,
                    "ocr_confidence": None
                })

        if not files_for_processing:
            dozzle_log("warning", "⚠️ [OCR_TEMP_BATCH] No valid files to process",
                      claim_id=claim_id)
            return processing_results

        # Process files through OCR API using form data
        try:
            ocr_results = await self._submit_temp_files_to_ocr_api(files_for_processing, claim_id)
            processing_results.extend(ocr_results)

        except Exception as e:
            dozzle_log("error", "❌ [OCR_TEMP_BATCH] OCR API processing failed",
                      claim_id=claim_id,
                      error=str(e))

            # Mark all files as failed
            for file_info in files_for_processing:
                processing_results.append({
                    "filename": file_info["filename"],
                    "status": "ocr_api_failed",
                    "error": str(e),
                    "ocr_text": None,
                    "ocr_confidence": None
                })

        return processing_results

    async def _submit_temp_files_to_ocr_api(self,
                                          files_for_processing: List[Dict[str, Any]],
                                          claim_id: str) -> List[Dict[str, Any]]:
        """
        Submit temp files to OCR API using form data

        This method uses clean files from tmp/claimid/files/ and submits them
        as form data to avoid any email header contamination issues.
        """
        results = []

        try:
            # Prepare form data with files
            form_data = aiohttp.FormData()

            # Add OCR configuration
            form_data.add_field('config', json.dumps(self.ocr_config))

            # Add each file to form data
            file_handles = []
            for file_info in files_for_processing:
                try:
                    file_path = file_info["file_path"]
                    filename = file_info["filename"]

                    # Open file and add to form data
                    file_handle = open(file_path, 'rb')
                    file_handles.append(file_handle)

                    # Determine content type based on file extension
                    content_type = self._get_content_type_from_filename(filename)

                    form_data.add_field(
                        'files',
                        file_handle,
                        filename=filename,
                        content_type=content_type
                    )

                    dozzle_log("info", "📎 [OCR_FORM_DATA] Added file to form data",
                              filename=filename,
                              content_type=content_type,
                              file_size=file_info["file_size"])

                except Exception as e:
                    dozzle_log("error", "❌ [OCR_FORM_DATA] Failed to add file to form data",
                              filename=file_info["filename"],
                              error=str(e))
                    results.append({
                        "filename": file_info["filename"],
                        "status": "form_data_failed",
                        "error": str(e),
                        "ocr_text": None,
                        "ocr_confidence": None
                    })

            if not file_handles:
                dozzle_log("warning", "⚠️ [OCR_FORM_DATA] No files successfully added to form data",
                          claim_id=claim_id)
                return results

            try:
                # Submit to OCR API
                dozzle_log("info", "🚀 [OCR_API_SUBMIT] Submitting form data to OCR API",
                          claim_id=claim_id,
                          file_count=len(file_handles),
                          api_url=self.ocr_api_url)

                timeout = aiohttp.ClientTimeout(total=self.timeout)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.post(self.ocr_api_url, data=form_data) as response:
                        if response.status == 200:
                            response_data = await response.json()

                            # Process API response
                            api_results = self._parse_ocr_api_response(response_data, files_for_processing)
                            results.extend(api_results)

                            dozzle_log("info", "✅ [OCR_API_SUBMIT] Successfully processed files",
                                      claim_id=claim_id,
                                      processed_count=len(api_results))
                        else:
                            error_text = await response.text()
                            error_msg = f"OCR API returned status {response.status}: {error_text}"

                            dozzle_log("error", "❌ [OCR_API_SUBMIT] API request failed",
                                      claim_id=claim_id,
                                      status_code=response.status,
                                      error=error_text)

                            # Mark all files as failed
                            for file_info in files_for_processing:
                                results.append({
                                    "filename": file_info["filename"],
                                    "status": "api_error",
                                    "error": error_msg,
                                    "ocr_text": None,
                                    "ocr_confidence": None
                                })

            finally:
                # Always close file handles
                for file_handle in file_handles:
                    try:
                        file_handle.close()
                    except Exception as e:
                        dozzle_log("warning", "⚠️ [OCR_FORM_DATA] Failed to close file handle",
                                  error=str(e))

        except Exception as e:
            dozzle_log("error", "❌ [OCR_API_SUBMIT] Unexpected error during API submission",
                      claim_id=claim_id,
                      error=str(e),
                      error_type=type(e).__name__)

            # Mark all files as failed
            for file_info in files_for_processing:
                results.append({
                    "filename": file_info["filename"],
                    "status": "unexpected_error",
                    "error": str(e),
                    "ocr_text": None,
                    "ocr_confidence": None
                })

        return results

    def _get_content_type_from_filename(self, filename: str) -> str:
        """Get appropriate content type based on file extension"""
        import mimetypes

        content_type, _ = mimetypes.guess_type(filename)
        if content_type:
            return content_type

        # Fallback for common document types
        ext = os.path.splitext(filename)[1].lower()
        if ext == '.pdf':
            return 'application/pdf'
        elif ext in ['.jpg', '.jpeg']:
            return 'image/jpeg'
        elif ext == '.png':
            return 'image/png'
        elif ext == '.tiff':
            return 'image/tiff'
        else:
            return 'application/octet-stream'
    
    async def _process_attachments_batch(self, 
                                       attachments: List[Dict[str, Any]], 
                                       claim_id: str) -> List[Dict[str, Any]]:
        """
        Process attachments through Zurich OCR API in batch
        
        Args:
            attachments: List of attachment records from database
            claim_id: Claim ID for tracking
            
        Returns:
            List of processing results
        """
        processing_results = []
        
        # Create temporary directory for downloaded files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Download files from Supabase storage
            downloaded_files = []
            for attachment in attachments:
                try:
                    file_path = await self._download_attachment_file(attachment, temp_path)
                    if file_path:
                        downloaded_files.append({
                            "attachment": attachment,
                            "file_path": file_path
                        })
                except Exception as e:
                    dozzle_log("error", "❌ [OCR_SERVICE] Failed to download attachment",
                              attachment_id=attachment.get('id'),
                              filename=attachment.get('original_filename'),
                              error=str(e))
                    processing_results.append({
                        "attachment_id": attachment.get('id'),
                        "filename": attachment.get('original_filename'),
                        "status": "download_failed",
                        "error": str(e),
                        "ocr_text": None,
                        "ocr_confidence": None
                    })
            
            if not downloaded_files:
                dozzle_log("warning", "⚠️ [OCR_SERVICE] No files successfully downloaded",
                          claim_id=claim_id)
                return processing_results
            
            # Process through OCR API
            try:
                ocr_results = await self._call_ocr_api(downloaded_files)
                
                # Combine results
                for i, file_info in enumerate(downloaded_files):
                    attachment = file_info["attachment"]
                    
                    if i < len(ocr_results):
                        ocr_result = ocr_results[i]
                        processing_results.append({
                            "attachment_id": attachment.get('id'),
                            "filename": attachment.get('original_filename'),
                            "status": "success",
                            "error": None,
                            "ocr_text": ocr_result.get('extracted_text', ''),
                            "ocr_confidence": ocr_result.get('confidence', 0.0),
                            "document_type": ocr_result.get('document_type'),
                            "processing_metadata": ocr_result
                        })
                    else:
                        processing_results.append({
                            "attachment_id": attachment.get('id'),
                            "filename": attachment.get('original_filename'),
                            "status": "ocr_failed",
                            "error": "No OCR result returned",
                            "ocr_text": None,
                            "ocr_confidence": None
                        })
                        
            except Exception as e:
                dozzle_log("error", "❌ [OCR_SERVICE] OCR API call failed",
                          claim_id=claim_id,
                          error=str(e))
                
                # Mark all downloaded files as failed
                for file_info in downloaded_files:
                    attachment = file_info["attachment"]
                    processing_results.append({
                        "attachment_id": attachment.get('id'),
                        "filename": attachment.get('original_filename'),
                        "status": "api_failed",
                        "error": str(e),
                        "ocr_text": None,
                        "ocr_confidence": None
                    })
        
        return processing_results
    
    async def _download_attachment_file(self, 
                                      attachment: Dict[str, Any], 
                                      temp_path: Path) -> Optional[Path]:
        """
        Download attachment file from Supabase storage to temporary location
        
        Args:
            attachment: Attachment record from database
            temp_path: Temporary directory path
            
        Returns:
            Path to downloaded file or None if failed
        """
        try:
            attachment_id = attachment.get('id')
            filename = attachment.get('original_filename')
            storage_path = attachment.get('storage_path')
            
            if not storage_path:
                dozzle_log("warning", "⚠️ [OCR_SERVICE] No storage path for attachment",
                          attachment_id=attachment_id,
                          filename=filename)
                return None
            
            # Download file from Supabase storage
            file_content = await self.supabase.download_file(storage_path)
            
            if not file_content:
                dozzle_log("warning", "⚠️ [OCR_SERVICE] Empty file content",
                          attachment_id=attachment_id,
                          filename=filename)
                return None
            
            # Save to temporary file
            safe_filename = self._sanitize_filename(filename)
            file_path = temp_path / safe_filename
            
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
            
            dozzle_log("info", "📥 [OCR_SERVICE] File downloaded successfully",
                      attachment_id=attachment_id,
                      filename=filename,
                      file_size=len(file_content),
                      temp_path=str(file_path))
            
            return file_path
            
        except Exception as e:
            dozzle_log("error", "❌ [OCR_SERVICE] Failed to download file",
                      attachment_id=attachment.get('id'),
                      filename=attachment.get('original_filename'),
                      error=str(e))
            return None
    
    async def _call_ocr_api(self, downloaded_files: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Call Zurich OCR API with downloaded files
        
        Args:
            downloaded_files: List of downloaded file information
            
        Returns:
            List of OCR results
        """
        try:
            dozzle_log("info", "🔍 [OCR_SERVICE] Calling Zurich OCR API",
                      file_count=len(downloaded_files),
                      api_url=self.ocr_api_url)
            
            # Prepare multipart form data
            data = aiohttp.FormData()
            
            # Add config
            data.add_field('config', json.dumps(self.ocr_config))
            
            # Add files
            for file_info in downloaded_files:
                file_path = file_info["file_path"]
                filename = file_info["attachment"]["original_filename"]
                
                # Read file content
                async with aiofiles.open(file_path, 'rb') as f:
                    file_content = await f.read()

                # CRITICAL: Clean email headers from file content before OCR
                try:
                    from ..email_processing.email_monitor import EmailMonitor
                    # Create a temporary EmailMonitor instance for cleaning
                    email_monitor = EmailMonitor(settings=self.settings)
                    cleaned_content = email_monitor.clean_attachment_for_ocr(file_content, filename)

                    if len(cleaned_content) != len(file_content):
                        dozzle_log("info", "🧹 [OCR_CLEAN] File cleaned before OCR",
                                  filename=filename,
                                  original_size=len(file_content),
                                  cleaned_size=len(cleaned_content),
                                  reduction_bytes=len(file_content) - len(cleaned_content))
                        file_content = cleaned_content
                    else:
                        dozzle_log("debug", "✅ [OCR_CLEAN] No cleaning needed",
                                  filename=filename)

                except Exception as e:
                    dozzle_log("warning", "⚠️ [OCR_CLEAN] Failed to clean file, using original",
                              filename=filename,
                              error=str(e))
                    # Continue with original content if cleaning fails

                # Add to form data
                data.add_field('files', file_content, filename=filename)
            
            # Make API call
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(self.ocr_api_url, data=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        dozzle_log("info", "✅ [OCR_SERVICE] OCR API call successful",
                                  status_code=response.status,
                                  result_count=len(result.get('results', [])))
                        return result.get('results', [])
                    else:
                        error_text = await response.text()
                        dozzle_log("error", "❌ [OCR_SERVICE] OCR API call failed",
                                  status_code=response.status,
                                  error_text=error_text[:500])
                        raise Exception(f"OCR API failed with status {response.status}: {error_text}")
                        
        except Exception as e:
            dozzle_log("error", "❌ [OCR_SERVICE] OCR API call exception",
                      error=str(e),
                      error_type=type(e).__name__)
            raise
    
    async def _update_attachments_with_ocr_results(self, 
                                                 processing_results: List[Dict[str, Any]], 
                                                 claim_id: str) -> None:
        """
        Update attachment records in database with OCR results
        
        Args:
            processing_results: List of OCR processing results
            claim_id: Claim ID for tracking
        """
        try:
            for result in processing_results:
                attachment_id = result["attachment_id"]
                
                if result["status"] == "success":
                    # Update with OCR results
                    await self.supabase.update_attachment_ocr(
                        attachment_id=attachment_id,
                        ocr_text=result["ocr_text"],
                        ocr_confidence=result["ocr_confidence"],
                        document_type=result.get("document_type"),
                        processing_metadata=result.get("processing_metadata"),
                        processed_at=datetime.utcnow()
                    )
                    
                    dozzle_log("info", "✅ [OCR_SERVICE] Attachment OCR results updated",
                              attachment_id=attachment_id,
                              filename=result["filename"],
                              confidence=result["ocr_confidence"],
                              text_length=len(result["ocr_text"]) if result["ocr_text"] else 0)
                else:
                    # Update with error status
                    await self.supabase.update_attachment_ocr(
                        attachment_id=attachment_id,
                        ocr_text=None,
                        ocr_confidence=None,
                        document_type=None,
                        processing_metadata={"error": result["error"], "status": result["status"]},
                        processed_at=datetime.utcnow()
                    )
                    
                    dozzle_log("warning", "⚠️ [OCR_SERVICE] Attachment marked as failed",
                              attachment_id=attachment_id,
                              filename=result["filename"],
                              error=result["error"])
            
            dozzle_log("info", "✅ [OCR_SERVICE] All attachment OCR results updated",
                      claim_id=claim_id,
                      total_results=len(processing_results))
            
        except Exception as e:
            dozzle_log("error", "❌ [OCR_SERVICE] Failed to update attachment OCR results",
                      claim_id=claim_id,
                      error=str(e))
            raise
    
    async def _add_processing_completed_comment(self, 
                                              zendesk_ticket_id: str, 
                                              processing_results: List[Dict[str, Any]]) -> None:
        """Add completion comment to Zendesk with OCR results summary"""
        try:
            successful_results = [r for r in processing_results if r["status"] == "success"]
            failed_results = [r for r in processing_results if r["status"] != "success"]
            
            comment = f"""DOCUMENT PROCESSING COMPLETED

OCR processing has been completed for all uploaded documents.

Processing Summary:
• Successfully processed: {len(successful_results)} documents
• Failed to process: {len(failed_results)} documents
• Total documents: {len(processing_results)}

"""
            
            if successful_results:
                comment += "Successfully Processed Documents:\n"
                for result in successful_results:
                    confidence = result.get("ocr_confidence", 0.0)
                    text_length = len(result.get("ocr_text", "")) if result.get("ocr_text") else 0
                    comment += f"• {result['filename']} (Confidence: {confidence:.1%}, Text: {text_length} chars)\n"
                comment += "\n"
            
            if failed_results:
                comment += "Failed to Process:\n"
                for result in failed_results:
                    comment += f"• {result['filename']} ({result['error']})\n"
                comment += "\n"
            
            comment += """Next Steps:
• Extracted text content is now available for analysis
• Documents will be analyzed for claim details
• Additional processing may be triggered based on content

All extracted text has been securely stored and is available for claim processing."""

            dozzle_log("info", "💬 [OCR_SERVICE] Adding OCR processing completion comment",
                      zendesk_ticket_id=zendesk_ticket_id,
                      successful_count=len(successful_results),
                      failed_count=len(failed_results))

            # Add completion comment to Zendesk ticket if client is available
            if self.zendesk:
                comment_success = await self.zendesk.add_comment_to_ticket(
                    ticket_id=zendesk_ticket_id,
                    comment=comment,
                    public=False  # Internal comment for agent use
                )
                
                if comment_success:
                    dozzle_log("info", "✅ [OCR_SERVICE] Processing completion comment added to Zendesk",
                              zendesk_ticket_id=zendesk_ticket_id,
                              comment_added=True,
                              successful_count=len(successful_results),
                              failed_count=len(failed_results))
                else:
                    dozzle_log("warning", "⚠️ [OCR_SERVICE] Failed to add processing completion comment",
                              zendesk_ticket_id=zendesk_ticket_id,
                              comment_added=False)
            else:
                dozzle_log("warning", "⚠️ [OCR_SERVICE] No Zendesk client available for completion comment",
                          zendesk_ticket_id=zendesk_ticket_id)
            
        except Exception as e:
            dozzle_log("error", "❌ [OCR_SERVICE] Failed to add processing completion comment",
                      zendesk_ticket_id=zendesk_ticket_id,
                      error=str(e),
                      error_type=type(e).__name__)
    
    async def _log_comprehensive_results(self, 
                                       claim_id: str, 
                                       workflow_id: str,
                                       processing_results: List[Dict[str, Any]]) -> None:
        """Log comprehensive OCR processing results with all email and extraction details"""
        try:
            # Get email and claim details for comprehensive logging
            claim_details = await self.supabase.get_claim_by_id(claim_id)
            
            if not claim_details:
                dozzle_log("warning", "⚠️ [OCR_SERVICE] Could not retrieve claim details for comprehensive logging",
                          claim_id=claim_id)
                return
            
            # Extract email information
            email_subject = claim_details.get('email_subject', 'No subject available')
            email_body = claim_details.get('email_body', 'No email body available')
            
            # Get classification/analysis results if available
            classification_summary = claim_details.get('analysis_result', {})
            if isinstance(classification_summary, str):
                try:
                    import json
                    classification_summary = json.loads(classification_summary)
                except:
                    classification_summary = {'raw_result': classification_summary}
            
            # Calculate processing statistics
            successful_results = [r for r in processing_results if r["status"] == "success"]
            failed_results = [r for r in processing_results if r["status"] != "success"]
            total_text_extracted = sum(
                len(r.get("ocr_text", "")) for r in successful_results
            )
            
            # Extract all filenames
            all_filenames = [r["filename"] for r in processing_results]
            
            # Generate timestamp
            processing_timestamp = datetime.now().isoformat()
            
            # 🎯 COMPREHENSIVE FINAL LOG - All requested details
            dozzle_log("info", "📋 [OCR_COMPREHENSIVE_SUMMARY] Complete OCR processing results",
                      # ✅ Email Subject
                      email_subject=email_subject,
                      
                      # ✅ Email Body
                      email_body=email_body,
                      
                      # ✅ List of all filenames
                      all_filenames=all_filenames,
                      filenames_count=len(all_filenames),
                      
                      # ✅ Summary of LLM email classification/extraction
                      llm_classification_summary={
                          'claim_type': classification_summary.get('claim_type', 'Not classified'),
                          'urgency_level': classification_summary.get('urgency_level', 'Unknown'),
                          'confidence_score': classification_summary.get('confidence_score', 'N/A'),
                          'key_entities': classification_summary.get('entities_extracted', []),
                          'analysis_model': classification_summary.get('model_used', 'Unknown'),
                          'processing_time': classification_summary.get('processing_time_ms', 'Unknown'),
                          'full_classification': classification_summary
                      },
                      
                      # ✅ OCR extraction outputs (one per file)
                      ocr_extraction_outputs=[
                          {
                              'filename': result["filename"],
                              'status': result["status"],
                              'confidence_score': result.get("ocr_confidence"),
                              'text_length_characters': len(result.get("ocr_text", "")),
                              'text_preview': result.get("ocr_text", "")[:200] + "..." if len(result.get("ocr_text", "")) > 200 else result.get("ocr_text", ""),
                              'full_text_reference': f"attachment_id_{result.get('attachment_id', 'unknown')}",
                              'processing_error': result.get("error") if result["status"] != "success" else None,
                              'document_type': result.get("document_type", "Unknown"),
                              'file_size_bytes': result.get("file_size", 0)
                          }
                          for result in processing_results
                      ],
                      
                      # ✅ Timestamp
                      processing_timestamp=processing_timestamp,
                      
                      # Additional processing statistics
                      processing_statistics={
                          'total_files_processed': len(processing_results),
                          'successful_extractions': len(successful_results),
                          'failed_extractions': len(failed_results),
                          'total_text_characters_extracted': total_text_extracted,
                          'average_confidence': sum(r.get("ocr_confidence", 0) for r in successful_results) / len(successful_results) if successful_results else 0,
                          'processing_duration_iso': processing_timestamp,
                          'claim_id': claim_id,
                          'workflow_id': workflow_id
                      })
            
            # Additional detailed logs for each OCR extraction
            for i, result in enumerate(processing_results, 1):
                dozzle_log("info", f"📄 [OCR_FILE_{i}] Individual file extraction details",
                          filename=result["filename"],
                          extraction_status=result["status"],
                          ocr_confidence_percent=f"{result.get('ocr_confidence', 0)*100:.1f}%" if result.get('ocr_confidence') else "N/A",
                          text_extracted_length=len(result.get("ocr_text", "")),
                          text_sample=result.get("ocr_text", "")[:100] + "..." if len(result.get("ocr_text", "")) > 100 else result.get("ocr_text", ""),
                          attachment_id=result.get("attachment_id"),
                          error_details=result.get("error") if result["status"] != "success" else None,
                          file_index=i,
                          total_files=len(processing_results))
            
            dozzle_log("info", "🏁 [OCR_SERVICE] Comprehensive OCR processing completed",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      final_status="completed",
                      summary_logged=True,
                      all_files_logged=True,
                      timestamp=processing_timestamp)
            
            await self._log_final_comprehensive_summary(claim_id, workflow_id, processing_results, claim_details)
            
        except Exception as e:
            dozzle_log("error", "❌ [OCR_SERVICE] Error in comprehensive results logging",
                      claim_id=claim_id,
                      error=str(e),
                      timestamp=datetime.now().isoformat())
            # Don't raise - logging failure shouldn't break the workflow
    
    async def _log_final_comprehensive_summary(self, 
                                          claim_id: str, 
                                          workflow_id: str,
                                          processing_results: List[Dict[str, Any]],
                                          email_data: Dict[str, Any]) -> None:
        """Log comprehensive final summary with all requested details"""
        try:
            # Get current timestamp
            final_timestamp = datetime.now().isoformat()
            
            # Extract email details
            email_subject = email_data.get('subject', 'Unknown Subject')
            email_body = email_data.get('body', 'No body content')
            sender_email = email_data.get('sender_email', 'Unknown Sender')
            
            # Extract all filenames from processing results
            all_filenames = [result.get('filename', 'Unknown File') for result in processing_results]
            
            # Get LLM email classification summary
            llm_classification = email_data.get('classification', {})
            llm_summary = {
                'claim_type': llm_classification.get('claim_type', 'Unknown'),
                'urgency_level': llm_classification.get('urgency_level', 'Unknown'),
                'confidence_score': llm_classification.get('confidence_score', 0.0),
                'key_insights': llm_classification.get('key_insights', [])
            }
            
            # Prepare OCR extraction outputs
            ocr_extractions = []
            total_text_extracted = 0
            successful_extractions = 0
            
            for result in processing_results:
                extraction_output = {
                    'filename': result.get('filename', 'Unknown'),
                    'status': result.get('status', 'unknown'),
                    'confidence_score': result.get('confidence', 0.0),
                    'text_length': len(result.get('text', '')),
                    'text_preview': result.get('text', '')[:200] + '...' if len(result.get('text', '')) > 200 else result.get('text', ''),
                    'full_text_reference': f"Full text stored in database for claim {claim_id}",
                    'processing_metadata': result.get('processing_metadata', {})
                }
                ocr_extractions.append(extraction_output)
                
                if result.get('status') == 'success':
                    successful_extractions += 1
                    total_text_extracted += len(result.get('text', ''))
            
            # Create comprehensive final summary
            comprehensive_summary = {
                'FINAL_OCR_PROCESSING_SUMMARY': {
                    'timestamp': final_timestamp,
                    'claim_id': claim_id,
                    'workflow_id': workflow_id,
                    
                    # ✅ Email Details
                    'email_subject': email_subject,
                    'email_body': email_body,
                    'sender_email': sender_email,
                    
                    # ✅ File Processing Summary
                    'list_of_all_filenames': all_filenames,
                    'total_files_processed': len(processing_results),
                    'successful_extractions': successful_extractions,
                    'failed_extractions': len(processing_results) - successful_extractions,
                    
                    # ✅ LLM Email Classification Summary
                    'llm_email_classification_summary': llm_summary,
                    
                    # ✅ OCR Extraction Outputs (one per file)
                    'ocr_extraction_outputs': ocr_extractions,
                    
                    # Statistics
                    'processing_statistics': {
                        'total_characters_extracted': total_text_extracted,
                        'average_confidence_score': sum(r.get('confidence', 0) for r in processing_results) / len(processing_results) if processing_results else 0,
                        'processing_duration': 'Calculated from workflow start',
                        'success_rate': f"{(successful_extractions / len(processing_results) * 100):.1f}%" if processing_results else "0%"
                    }
                }
            }
            
            # Log the comprehensive summary with Dozzle
            dozzle_log("info", "🎯 [FINAL_COMPREHENSIVE_SUMMARY] Complete OCR processing summary",
                      **comprehensive_summary['FINAL_OCR_PROCESSING_SUMMARY'])
            
            # Also log individual file details for better tracking
            for i, extraction in enumerate(ocr_extractions, 1):
                dozzle_log("info", f"📄 [FILE_{i}_DETAILS] Individual file extraction summary",
                          filename=extraction['filename'],
                          status=extraction['status'],
                          confidence=extraction['confidence_score'],
                          text_length=extraction['text_length'],
                          text_preview=extraction['text_preview'])
            
            dozzle_log("info", "✅ [COMPREHENSIVE_LOGGING] Final summary logged successfully",
                      claim_id=claim_id,
                      total_files=len(all_filenames),
                      successful_ocr=successful_extractions,
                      total_text_characters=total_text_extracted,
                      timestamp=final_timestamp)
                      
        except Exception as e:
            dozzle_log("error", "❌ [COMPREHENSIVE_LOGGING] Failed to log final summary",
                      claim_id=claim_id,
                      error=str(e))
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe file system usage"""
        import re
        # Remove or replace invalid characters
        safe_filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Limit length
        if len(safe_filename) > 255:
            name, ext = os.path.splitext(safe_filename)
            safe_filename = name[:255-len(ext)] + ext
        return safe_filename
    
    def get_status(self) -> Dict[str, Any]:
        """Get OCR service status"""
        return {
            "status": "operational",
            "service": "zurich_ocr_service",
            "api_url": self.ocr_api_url,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "version": "1.0.0"
        } 