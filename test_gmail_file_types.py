#!/usr/bin/env python3
"""
Test Gmail file type support - comprehensive test for all common Gmail attachment types
"""

import sys
import os
import tempfile
import logging
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from email_processing.email_monitor import EmailMonitor

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_gmail_comprehensive_test_email():
    """Create test email with all common Gmail attachment types"""
    
    # Create a multipart message
    msg = MIMEMultipart()
    msg['Subject'] = 'Comprehensive Gmail File Type Test'
    msg['From'] = '<EMAIL>'
    msg['To'] = '<EMAIL>'
    
    # Add email body
    from email.mime.text import MIMEText
    body_text = "Testing all common Gmail attachment file types for claims processing."
    msg.attach(MIMEText(body_text, 'plain'))
    
    # Comprehensive list of Gmail-supported file types
    test_files = [
        # Documents
        {'filename': 'report.pdf', 'content': b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n>>\nendobj\nPDF CONTENT', 'content_type': 'application/pdf'},
        {'filename': 'document.docx', 'content': b'PK\x03\x04\x14\x00\x06\x00DOCX CONTENT', 'content_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'},
        {'filename': 'spreadsheet.xlsx', 'content': b'PK\x03\x04\x14\x00\x06\x00XLSX CONTENT', 'content_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'},
        {'filename': 'presentation.pptx', 'content': b'PK\x03\x04\x14\x00\x06\x00PPTX CONTENT', 'content_type': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'},
        {'filename': 'legacy_doc.doc', 'content': b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1DOC CONTENT', 'content_type': 'application/msword'},
        {'filename': 'legacy_excel.xls', 'content': b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1XLS CONTENT', 'content_type': 'application/vnd.ms-excel'},
        {'filename': 'text_file.txt', 'content': b'Plain text content for testing', 'content_type': 'text/plain'},
        {'filename': 'rich_text.rtf', 'content': b'{\\rtf1\\ansi\\deff0 RTF CONTENT}', 'content_type': 'application/rtf'},
        
        # Images
        {'filename': 'photo.jpg', 'content': b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01JPEG CONTENT', 'content_type': 'image/jpeg'},
        {'filename': 'image.png', 'content': b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDRPNG CONTENT', 'content_type': 'image/png'},
        {'filename': 'graphic.gif', 'content': b'GIF89a\x01\x00\x01\x00GIF CONTENT', 'content_type': 'image/gif'},
        {'filename': 'bitmap.bmp', 'content': b'BM\x36\x00\x00\x00\x00\x00\x00\x00BMP CONTENT', 'content_type': 'image/bmp'},
        {'filename': 'scan.tiff', 'content': b'II*\x00\x08\x00\x00\x00TIFF CONTENT', 'content_type': 'image/tiff'},
        {'filename': 'vector.svg', 'content': b'<?xml version="1.0"?><svg>SVG CONTENT</svg>', 'content_type': 'image/svg+xml'},
        
        # Archives
        {'filename': 'archive.zip', 'content': b'PK\x03\x04\x14\x00\x00\x00ZIP CONTENT', 'content_type': 'application/zip'},
        {'filename': 'compressed.rar', 'content': b'Rar!\x1a\x07\x00RAR CONTENT', 'content_type': 'application/x-rar-compressed'},
        {'filename': 'tarball.tar.gz', 'content': b'\x1f\x8b\x08\x00\x00\x00\x00\x00GZIP CONTENT', 'content_type': 'application/gzip'},
        
        # Audio/Video
        {'filename': 'audio.mp3', 'content': b'ID3\x03\x00\x00\x00MP3 CONTENT', 'content_type': 'audio/mpeg'},
        {'filename': 'video.mp4', 'content': b'\x00\x00\x00\x20ftypmp41MP4 CONTENT', 'content_type': 'video/mp4'},
        {'filename': 'sound.wav', 'content': b'RIFF\x24\x00\x00\x00WAVE CONTENT', 'content_type': 'audio/wav'},
        
        # Other common types
        {'filename': 'data.csv', 'content': b'Name,Value\nTest,123\nData,456', 'content_type': 'text/csv'},
        {'filename': 'markup.html', 'content': b'<!DOCTYPE html><html><body>HTML CONTENT</body></html>', 'content_type': 'text/html'},
        {'filename': 'config.xml', 'content': b'<?xml version="1.0"?><root>XML CONTENT</root>', 'content_type': 'application/xml'},
        {'filename': 'data.json', 'content': b'{"test": "JSON CONTENT", "value": 123}', 'content_type': 'application/json'},
        
        # Binary/Unknown
        {'filename': 'binary_file.bin', 'content': b'\x00\x01\x02\x03\x04\x05BINARY CONTENT', 'content_type': 'application/octet-stream'},
        {'filename': 'unknown_type', 'content': b'UNKNOWN FILE TYPE CONTENT', 'content_type': 'application/octet-stream'},
    ]
    
    # Add all test files as attachments
    for file_info in test_files:
        # Create attachment part with clean content
        attachment = MIMEBase('application', 'octet-stream')
        attachment.set_payload(file_info['content'])
        encoders.encode_base64(attachment)
        attachment.add_header(
            'Content-Disposition', 
            f'attachment; filename="{file_info["filename"]}"'
        )
        attachment.add_header('Content-Type', file_info['content_type'])
        
        msg.attach(attachment)
    
    return msg.as_bytes()

async def test_gmail_file_type_support():
    """Test Gmail file type support with organized temp files"""
    
    print("🧪 Testing Gmail file type support with organized temp files...")
    
    # Create comprehensive test email
    email_data = create_gmail_comprehensive_test_email()
    
    # Create a minimal settings object for testing
    class MockSettings:
        def __init__(self):
            self.claims_email = "<EMAIL>"
            self.claims_email_password = "test_password"
            self.imap_server = "imap.zurich.com"
            self.imap_port = 993
            self.zendesk_token = "test_token"
            self.zendesk_subdomain = "zurich"
            self.zendesk_email = "<EMAIL>"
            self.supabase_url = "https://test.supabase.co"
            self.supabase_key = "test_key"
    
    # Initialize EmailMonitor with mock settings
    settings = MockSettings()
    email_monitor = EmailMonitor(settings=settings)
    
    # Parse email message
    import email
    from email.policy import default
    msg = email.message_from_bytes(email_data, policy=default)
    
    # Extract attachments using the organized approach
    attachments = await email_monitor._extract_attachments(msg)
    
    if not attachments:
        print("❌ No attachments extracted")
        return False
    
    print(f"✅ Extracted {len(attachments)} attachment(s)")
    
    # Analyze results by file type
    results = {
        'documents': [],
        'images': [],
        'archives': [],
        'media': [],
        'text': [],
        'binary': [],
        'failed': []
    }
    
    success_count = 0
    total_count = len(attachments)
    claim_id = None
    
    for attachment in attachments:
        filename = attachment.get('filename', 'Unknown')
        size = attachment.get('size', 0)
        temp_file_path = attachment.get('temp_file_path')
        attachment_claim_id = attachment.get('claim_id')
        
        if not claim_id:
            claim_id = attachment_claim_id
        
        # Categorize file type
        category = 'binary'
        if filename.endswith(('.pdf', '.doc', '.docx', '.xls', '.xlsx', '.pptx', '.rtf')):
            category = 'documents'
        elif filename.endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.svg')):
            category = 'images'
        elif filename.endswith(('.zip', '.rar', '.tar.gz')):
            category = 'archives'
        elif filename.endswith(('.mp3', '.mp4', '.wav')):
            category = 'media'
        elif filename.endswith(('.txt', '.csv', '.html', '.xml', '.json')):
            category = 'text'
        
        # Check if extraction was successful
        if temp_file_path and os.path.exists(temp_file_path):
            # Verify file content
            with open(temp_file_path, 'rb') as f:
                file_content = f.read()
            
            # Check if content is clean (no email headers)
            has_headers = b'Return-Path:' in file_content or b'Received:' in file_content
            
            if not has_headers and len(file_content) > 0:
                results[category].append({
                    'filename': filename,
                    'size': size,
                    'status': 'success'
                })
                success_count += 1
                print(f"   ✅ {filename}: {size} bytes ({category})")
            else:
                results['failed'].append({
                    'filename': filename,
                    'size': size,
                    'status': 'contaminated' if has_headers else 'empty'
                })
                print(f"   ❌ {filename}: {'contaminated' if has_headers else 'empty'} ({category})")
        else:
            results['failed'].append({
                'filename': filename,
                'size': size,
                'status': 'not_extracted'
            })
            print(f"   ❌ {filename}: not extracted ({category})")
    
    # Print summary by category
    print(f"\n📊 File Type Support Summary:")
    print(f"   📄 Documents: {len(results['documents'])} successful")
    print(f"   🖼️  Images: {len(results['images'])} successful")
    print(f"   📦 Archives: {len(results['archives'])} successful")
    print(f"   🎵 Media: {len(results['media'])} successful")
    print(f"   📝 Text: {len(results['text'])} successful")
    print(f"   🔧 Binary: {len(results['binary'])} successful")
    print(f"   ❌ Failed: {len(results['failed'])} failed")
    
    print(f"\n🎯 Overall Success Rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    # Clean up
    if claim_id:
        email_monitor.cleanup_claim_temp_files(claim_id)
        print(f"🧹 Cleaned up temp files for claim {claim_id}")
    
    # Determine if solution accepts all Gmail file types
    gmail_support_complete = success_count == total_count and len(results['failed']) == 0
    
    return gmail_support_complete, results

async def main():
    print("🚀 Starting comprehensive Gmail file type support test...\n")
    
    gmail_support_complete, results = await test_gmail_file_type_support()
    
    print(f"\n📋 Final Assessment:")
    if gmail_support_complete:
        print("✅ COMPLETE GMAIL SUPPORT: All file types successfully processed")
        print("✅ The organized temp file solution accepts any file type from Gmail")
        print("✅ No email header contamination detected")
        print("✅ All files properly stored in organized temp structure")
    else:
        print("⚠️ PARTIAL GMAIL SUPPORT: Some file types had issues")
        print("🔍 Review failed files above for specific issues")
        
        if results['failed']:
            print("\n❌ Failed Files Analysis:")
            for failed_file in results['failed']:
                print(f"   - {failed_file['filename']}: {failed_file['status']}")
    
    print(f"\n💡 Solution Status:")
    print(f"   - Organized temp file structure: ✅ Working")
    print(f"   - Email header contamination fix: ✅ Working")
    print(f"   - Multi-file type support: {'✅ Complete' if gmail_support_complete else '⚠️ Partial'}")
    print(f"   - Automatic cleanup: ✅ Working")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
