#!/usr/bin/env python3
"""
🔍 Email Threading Diagnostic Test

Tests and validates the email threading implementation to ensure
proper In-Reply-To and References headers are being set correctly.
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any
import uuid

# Import Zurich email services
from src.communications.email_service import ProfessionalEmailService, EmailThreadInfo, EmailParser
from src.communications.email_threading import EmailThreadingManager
from src.config.settings import Settings


class EmailThreadingDiagnostic:
    """Comprehensive email threading diagnostic tool"""
    
    def __init__(self):
        self.settings = Settings()
        self.email_service = ProfessionalEmailService(self.settings)
        self.threading_manager = EmailThreadingManager()
        
    async def run_full_diagnosis(self) -> bool:
        """Run complete email threading diagnosis"""
        print("🔍 EMAIL THREADING DIAGNOSTIC TEST")
        print("=" * 50)
        
        # Test 1: Threading Manager
        test1_result = await self.test_threading_manager()
        
        # Test 2: Thread Info Extraction  
        test2_result = await self.test_thread_info_extraction()
        
        # Test 3: Email Header Generation
        test3_result = await self.test_email_header_generation()
        
        # Test 4: End-to-End Threading
        test4_result = await self.test_end_to_end_threading()
        
        # Test 5: Gmail-Specific Threading
        test5_result = await self.test_gmail_threading_compatibility()
        
        # Summary
        all_passed = all([test1_result, test2_result, test3_result, test4_result, test5_result])
        
        print("\n" + "=" * 50)
        print("📊 DIAGNOSTIC SUMMARY:")
        print(f"  Threading Manager: {'✅ PASS' if test1_result else '❌ FAIL'}")
        print(f"  Thread Extraction: {'✅ PASS' if test2_result else '❌ FAIL'}")
        print(f"  Header Generation: {'✅ PASS' if test3_result else '❌ FAIL'}")
        print(f"  End-to-End Test:   {'✅ PASS' if test4_result else '❌ FAIL'}")
        print(f"  Gmail Compatibility: {'✅ PASS' if test5_result else '❌ FAIL'}")
        print(f"\n🎯 OVERALL RESULT: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
        
        if not all_passed:
            print("\n🔧 RECOMMENDED FIXES:")
            await self.suggest_fixes()
        
        return all_passed
    
    async def test_threading_manager(self) -> bool:
        """Test the EmailThreadingManager functionality"""
        print("\n1️⃣ Testing Threading Manager...")
        
        try:
            # Test Message-ID generation
            claim_id = "PI12345678"
            message_id = self.threading_manager.generate_message_id(claim_id)
            
            print(f"   Generated Message-ID: {message_id}")
            
            # Validate Message-ID format
            if not message_id.startswith('<') or not message_id.endswith('>'):
                print("   ❌ Message-ID not properly formatted with < >")
                return False
                
            if claim_id not in message_id:
                print("   ❌ Message-ID doesn't contain claim reference")
                return False
            
            # Test thread header creation
            original_msg_id = "<<EMAIL>>"
            references = ["<<EMAIL>>", "<<EMAIL>>"]
            
            headers = self.threading_manager.create_thread_headers(
                message_id=message_id,
                in_reply_to=original_msg_id,
                references=references
            )
            
            print(f"   Threading Headers Generated:")
            for key, value in headers.items():
                print(f"     {key}: {value}")
            
            # Validate required headers
            required_headers = ['Message-ID', 'In-Reply-To', 'References']
            for header in required_headers:
                if header not in headers:
                    print(f"   ❌ Missing required header: {header}")
                    return False
            
            # Validate In-Reply-To format
            if headers['In-Reply-To'] != original_msg_id:
                print(f"   ❌ In-Reply-To mismatch: expected {original_msg_id}, got {headers['In-Reply-To']}")
                return False
            
            # Validate References includes original message
            if original_msg_id not in headers['References']:
                print(f"   ❌ References doesn't include original message ID")
                return False
            
            print("   ✅ Threading Manager working correctly")
            return True
            
        except Exception as e:
            print(f"   ❌ Threading Manager error: {e}")
            return False
    
    async def test_thread_info_extraction(self) -> bool:
        """Test EmailThreadInfo extraction from email data"""
        print("\n2️⃣ Testing Thread Info Extraction...")
        
        try:
            # Mock email data (simulating what we get from email processing)
            sample_email_data = {
                'message_id': '<<EMAIL>>',
                'subject': 'Car accident claim - need assistance',
                'sender_name': 'John Doe',
                'sender_email': '<EMAIL>', 
                'body': 'I had a car accident yesterday and need to file a claim...',
                'received_at': '2024-12-28T10:30:00Z',
                'references': '<<EMAIL>> <<EMAIL>>'
            }
            
            # Test the extraction method from ZendeskClient
            from src.zendesk_integration.zendesk_client import ZendeskClient
            from src.database.supabase_client import SupabaseClient
            
            supabase = SupabaseClient(self.settings)  
            zendesk = ZendeskClient(self.settings, supabase)
            
            thread_info = zendesk._extract_thread_info_from_email_data(sample_email_data)
            
            if not thread_info:
                print("   ❌ Thread info extraction returned None")
                return False
            
            print(f"   Extracted Thread Info:")
            print(f"     Message-ID: {thread_info.message_id}")
            print(f"     Original Subject: {thread_info.original_subject}")
            print(f"     Original Sender: {thread_info.original_sender}")
            print(f"     References Count: {len(thread_info.references)}")
            print(f"     Original Date: {thread_info.original_date}")
            
            # Validate extraction
            if thread_info.message_id != '<<EMAIL>>':
                print("   ❌ Message-ID extraction failed")
                return False
                
            if 'Car accident claim' not in thread_info.original_subject:
                print("   ❌ Subject extraction failed")
                return False
            
            if thread_info.original_sender != 'John Doe':
                print("   ❌ Sender extraction failed")
                return False
            
            print("   ✅ Thread Info extraction working correctly")
            return True
            
        except Exception as e:
            print(f"   ❌ Thread Info extraction error: {e}")
            return False
    
    async def test_email_header_generation(self) -> bool:
        """Test email header generation in email service"""
        print("\n3️⃣ Testing Email Header Generation...")
        
        try:
            # Create sample thread info
            thread_info = EmailThreadInfo(
                message_id='<<EMAIL>>',
                original_subject='Car accident claim submission',
                original_sender='<EMAIL>',
                original_date=datetime.now(),
                references=['<<EMAIL>>', '<<EMAIL>>']
            )
            
            # Test subject generation
            claim_id = "PI12345678"
            subject = self.email_service._generate_acknowledgment_subject(
                claim_id, "Auto Insurance Claim", thread_info
            )
            
            print(f"   Generated Subject: {subject}")
            
            # Should start with "Re:" for threading
            if not subject.startswith('Re:'):
                print("   ❌ Subject doesn't start with 'Re:' for threading")
                return False
            
            # Test threading header creation (simulating what happens in send_claim_acknowledgment)
            reply_message_id = self.threading_manager.generate_message_id(claim_id)
            thread_headers = self.threading_manager.create_thread_headers(
                message_id=reply_message_id,
                in_reply_to=thread_info.message_id,
                references=thread_info.references
            )
            
            print(f"   Reply Threading Headers:")
            for key, value in thread_headers.items():
                print(f"     {key}: {value}")
            
            # Validate In-Reply-To points to original message
            if thread_headers['In-Reply-To'] != thread_info.message_id:
                print("   ❌ In-Reply-To doesn't match original Message-ID")
                return False
            
            print("   ✅ Email header generation working correctly")
            return True
            
        except Exception as e:
            print(f"   ❌ Email header generation error: {e}")
            return False
    
    async def test_end_to_end_threading(self) -> bool:
        """Test complete end-to-end threading flow"""
        print("\n4️⃣ Testing End-to-End Threading Flow...")
        
        try:
            # Simulate complete flow from email receipt to acknowledgment
            print("   📨 Simulating customer email receipt...")
            
            # Original customer email data
            customer_email_data = {
                'message_id': '<<EMAIL>>',
                'subject': 'Property damage claim - water leak in basement',
                'sender_name': 'Sarah Johnson',
                'sender_email': '<EMAIL>',
                'body': 'I discovered water damage in my basement this morning. The water heater seems to have leaked overnight...',
                'received_at': datetime.now().isoformat(),
                'references': []
            }
            
            # Extract threading info (as ZendeskClient would do)
            from src.zendesk_integration.zendesk_client import ZendeskClient
            from src.database.supabase_client import SupabaseClient
            
            supabase = SupabaseClient(self.settings)
            zendesk = ZendeskClient(self.settings, supabase)
            
            thread_info = zendesk._extract_thread_info_from_email_data(customer_email_data)
            
            print("   🧵 Extracted threading information...")
            print(f"      Original Message-ID: {thread_info.message_id if thread_info else 'None'}")
            
            # Generate claim ID and reference
            claim_id = str(uuid.uuid4())
            claim_ref = f"PD{claim_id[:8].upper()}"
            
            print(f"   🎫 Generated claim reference: {claim_ref}")
            
            # Test acknowledgment email preparation (without actually sending)
            print("   📧 Preparing acknowledgment email...")
            
            # Generate Message-ID for reply
            reply_message_id = self.threading_manager.generate_message_id(claim_ref)
            
            # Create threading headers
            if thread_info:
                reply_headers = self.threading_manager.create_thread_headers(
                    message_id=reply_message_id,
                    in_reply_to=thread_info.message_id,
                    references=thread_info.references
                )
            else:
                reply_headers = self.threading_manager.create_thread_headers(
                    message_id=reply_message_id
                )
            
            print("   📋 Final threading headers for acknowledgment:")
            for key, value in reply_headers.items():
                print(f"      {key}: {value}")
            
            # Validate threading chain
            if thread_info and thread_info.message_id:
                if 'In-Reply-To' not in reply_headers:
                    print("   ❌ Missing In-Reply-To header in reply")
                    return False
                    
                if reply_headers['In-Reply-To'] != thread_info.message_id:
                    print("   ❌ In-Reply-To doesn't match original Message-ID")
                    return False
                    
                if thread_info.message_id not in reply_headers.get('References', ''):
                    print("   ❌ Original Message-ID not in References")
                    return False
            
            print("   ✅ End-to-end threading flow working correctly")
            return True
            
        except Exception as e:
            print(f"   ❌ End-to-end threading error: {e}")
            return False
    
    async def test_gmail_threading_compatibility(self) -> bool:
        """Test Gmail-specific threading requirements"""
        print("\n5️⃣ Testing Gmail Threading Compatibility...")
        
        try:
            # Gmail threading requirements:
            # 1. Same subject (with Re: prefix)
            # 2. In-Reply-To header with original Message-ID
            # 3. References header with message chain
            # 4. Same From address (or authorized alias)
            
            original_subject = "Auto accident claim - urgent assistance needed"
            
            # Test subject threading
            thread_info = EmailThreadInfo(
                message_id='<<EMAIL>>',
                original_subject=original_subject
            )
            
            reply_subject = self.email_service._generate_acknowledgment_subject(
                "AUTO12345678", "Auto Insurance Claim", thread_info
            )
            
            print(f"   Original Subject: {original_subject}")
            print(f"   Reply Subject: {reply_subject}")
            
            # Gmail expects "Re:" prefix for threading
            if not reply_subject.startswith('Re:'):
                print("   ❌ Gmail threading requires 'Re:' prefix")
                return False
            
            # Test that "Re: Re:" chains are avoided
            re_subject = "Re: " + original_subject
            thread_info_with_re = EmailThreadInfo(
                message_id='<<EMAIL>>',
                original_subject=re_subject
            )
            
            clean_reply_subject = self.email_service._generate_acknowledgment_subject(
                "AUTO12345679", "Auto Insurance Claim", thread_info_with_re
            )
            
            print(f"   Re: Subject Test: {clean_reply_subject}")
            
            if clean_reply_subject.count('Re:') > 1:
                print("   ❌ Multiple 'Re:' prefixes detected (should be cleaned)")
                return False
            
            # Test Message-ID format (Gmail prefers certain formats)
            claim_ref = "AUTO12345680"
            message_id = self.threading_manager.generate_message_id(claim_ref)
            
            print(f"   Generated Message-ID: {message_id}")
            
            # Should be properly formatted with domain
            if not message_id.startswith('<') or not message_id.endswith('>'):
                print("   ❌ Message-ID format not Gmail compatible")
                return False
            
            if '@' not in message_id:
                print("   ❌ Message-ID missing domain (Gmail requirement)")
                return False
            
            print("   ✅ Gmail threading compatibility verified")
            return True
            
        except Exception as e:
            print(f"   ❌ Gmail compatibility test error: {e}")
            return False
    
    async def suggest_fixes(self):
        """Suggest fixes based on diagnostic results"""
        print("\n🔧 SUGGESTED IMPROVEMENTS:")
        
        print("\n1. **Enhance Message-ID Extraction:**")
        print("   - Add fallback logic for missing Message-IDs")
        print("   - Validate Message-ID format before using")
        print("   - Log when threading info is missing")
        
        print("\n2. **Improve Email Client Compatibility:**")
        print("   - Ensure same From address for all claim emails")
        print("   - Add List-ID header for better organization")
        print("   - Include Message-ID in email logs for debugging")
        
        print("\n3. **Gmail-Specific Optimizations:**")
        print("   - Use consistent subject line patterns")
        print("   - Ensure References header includes full thread history")
        print("   - Add X-Gmail-Labels for better categorization")
        
        print("\n4. **Debug and Monitoring:**")
        print("   - Log all threading headers in production")
        print("   - Add threading success metrics")
        print("   - Monitor for broken thread chains")


async def main():
    """Run email threading diagnostic"""
    diagnostic = EmailThreadingDiagnostic()
    
    try:
        success = await diagnostic.run_full_diagnosis()
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n❌ DIAGNOSTIC FAILED: {e}")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main())) 