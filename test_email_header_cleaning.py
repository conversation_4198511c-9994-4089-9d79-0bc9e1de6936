#!/usr/bin/env python3
"""
Test Email Header Cleaning Functionality

This script tests the email header contamination cleaning functionality
to ensure that files with email headers are properly cleaned before OCR processing.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from email_processing.email_monitor import EmailMonitor
from config.settings import Settings


def test_email_header_cleaning():
    """Test the email header cleaning functionality"""
    
    print("🧪 Testing Email Header Cleaning Functionality")
    print("=" * 60)
    
    # Create a mock settings object
    class MockSettings:
        def __init__(self):
            self.claims_email = "<EMAIL>"
            self.claims_email_password = "test"
            self.imap_server = "imap.example.com"
            self.imap_port = 993
    
    settings = MockSettings()
    email_monitor = EmailMonitor(settings)
    
    # Test Case 1: Clean file content (should remain unchanged)
    print("\n📄 Test Case 1: Clean PDF content")
    clean_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\nThis is a clean PDF file content."
    result1 = email_monitor.clean_attachment_for_ocr(clean_content, "clean_document.pdf")
    
    if result1 == clean_content:
        print("✅ PASS: Clean content remained unchanged")
    else:
        print("❌ FAIL: Clean content was modified")
        print(f"   Original: {len(clean_content)} bytes")
        print(f"   Result: {len(result1)} bytes")
    
    # Test Case 2: Contaminated text file with email headers
    print("\n📧 Test Case 2: Contaminated text file with email headers")
    contaminated_content = b"""Content-Type: text/plain
Content-Disposition: attachment; filename="document.txt"
Content-Transfer-Encoding: quoted-printable
MIME-Version: 1.0
Message-ID: <<EMAIL>>
From: <EMAIL>
To: <EMAIL>
Subject: Test Email
Date: Mon, 1 Jan 2024 12:00:00 +0000

This is the actual document content that should be preserved.
It contains important information for the insurance claim.
The policy number is ABC123456.
"""

    result2 = email_monitor.clean_attachment_for_ocr(contaminated_content, "contaminated_document.txt")
    result2_text = result2.decode('utf-8', errors='ignore')

    # Check if email headers were removed
    headers_removed = (
        'Content-Type:' not in result2_text and
        'Message-ID:' not in result2_text and
        'From:' not in result2_text and
        'Subject:' not in result2_text
    )

    # Check if document content was preserved
    content_preserved = 'actual document content' in result2_text and 'policy number' in result2_text
    
    if headers_removed and content_preserved:
        print("✅ PASS: Email headers removed, document content preserved")
        print(f"   Original: {len(contaminated_content)} bytes")
        print(f"   Cleaned: {len(result2)} bytes")
        print(f"   Reduction: {len(contaminated_content) - len(result2)} bytes")
    else:
        print("❌ FAIL: Cleaning did not work as expected")
        print(f"   Headers removed: {headers_removed}")
        print(f"   Content preserved: {content_preserved}")
        print(f"   Result preview: {result2_text[:200]}...")
    
    # Test Case 3: File with "raw_email_header" contamination
    print("\n🔍 Test Case 3: File with 'raw_email_header' contamination")
    raw_header_content = b"""raw_email_header
Some other email contamination
Content-Type: text/plain

This is the actual document content that should be preserved.
It contains important information for the insurance claim.
"""
    
    result3 = email_monitor.clean_attachment_for_ocr(raw_header_content, "raw_header_document.txt")
    result3_text = result3.decode('utf-8', errors='ignore')
    
    # Check if raw_email_header was removed
    raw_header_removed = 'raw_email_header' not in result3_text
    
    # Check if actual content was preserved
    content_preserved = 'actual document content' in result3_text and 'insurance claim' in result3_text
    
    if raw_header_removed and content_preserved:
        print("✅ PASS: 'raw_email_header' contamination removed, content preserved")
        print(f"   Original: {len(raw_header_content)} bytes")
        print(f"   Cleaned: {len(result3)} bytes")
    else:
        print("❌ FAIL: Raw header cleaning did not work as expected")
        print(f"   Raw header removed: {raw_header_removed}")
        print(f"   Content preserved: {content_preserved}")
        print(f"   Result: {result3_text}")
    
    # Test Case 4: Binary file (should remain unchanged)
    print("\n🔢 Test Case 4: Binary file content")
    binary_content = bytes([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]) + b"PNG binary data here"
    result4 = email_monitor.clean_attachment_for_ocr(binary_content, "image.png")
    
    if result4 == binary_content:
        print("✅ PASS: Binary content remained unchanged")
    else:
        print("❌ FAIL: Binary content was modified")
    
    print("\n" + "=" * 60)
    print("🎯 Email Header Cleaning Test Complete")
    print("\nThis fix should resolve the 'raw_email_header' contamination in OCR results.")
    print("Files will now be cleaned before being sent to the OCR API.")


if __name__ == "__main__":
    test_email_header_cleaning()
