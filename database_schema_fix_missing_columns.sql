-- 🔧 DATABASE SCHEMA FIX - MISSING COLUMNS
-- This script adds ALL missing columns identified from error analysis
-- Run this script in your Supabase SQL Editor

-- ========================================
-- 1. FIX CLAIMS TABLE - ADD MISSING COLUMNS
-- ========================================

-- Add missing critical columns to claims table
ALTER TABLE claims
ADD COLUMN IF NOT EXISTS workflow_status TEXT NULL,
ADD COLUMN IF NOT EXISTS notes TEXT NULL,
ADD COLUMN IF NOT EXISTS claim_reference TEXT NULL;

-- Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_claims_workflow_status ON claims(workflow_status);
CREATE INDEX IF NOT EXISTS idx_claims_claim_reference ON claims(claim_reference);

-- ========================================
-- 2. MIGRATE EXISTING DATA
-- ========================================

-- Copy status to workflow_status for existing records
UPDATE claims 
SET workflow_status = status 
WHERE workflow_status IS NULL AND status IS NOT NULL;

-- ========================================
-- 3. VERIFY THE FIX
-- ========================================

-- Show the updated claims table schema
SELECT 'CLAIMS TABLE SCHEMA - AFTER FIX:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'claims' AND table_schema = 'public'
AND column_name IN ('status', 'workflow_status', 'notes', 'claim_reference')
ORDER BY column_name;

-- Show record counts
SELECT 
    'claims' as table_name, 
    count(*) as total_records,
    count(workflow_status) as with_workflow_status,
    count(notes) as with_notes,
    count(claim_reference) as with_claim_reference
FROM claims;

-- ========================================
-- 4. SUCCESS MESSAGE
-- ========================================
SELECT '✅ MISSING COLUMNS FIX COMPLETED!' as status;
SELECT 'Added: workflow_status, notes, claim_reference columns to claims table' as added_columns;
SELECT 'All new columns are nullable to prevent insertion errors' as note; 